// SEO Utility Functions for Oshudhigram

export interface SEOData {
  title: string
  description: string
  keywords: string[]
  image?: string
  url?: string
  type?: 'website' | 'article' | 'product'
  publishedTime?: string
  modifiedTime?: string
  author?: string
  price?: string
  currency?: string
  availability?: string
  brand?: string
  category?: string
}

// Generate optimized title with proper length and keywords
export function generateSEOTitle(title: string, siteName: string = 'Oshudhigram'): string {
  const maxLength = 60
  const fullTitle = `${title} | ${siteName}`
  
  if (fullTitle.length <= maxLength) {
    return fullTitle
  }
  
  // Truncate title if too long
  const availableLength = maxLength - siteName.length - 3 // 3 for " | "
  const truncatedTitle = title.substring(0, availableLength).trim()
  return `${truncatedTitle}... | ${siteName}`
}

// Generate optimized meta description
export function generateMetaDescription(description: string): string {
  const maxLength = 160
  
  if (description.length <= maxLength) {
    return description
  }
  
  // Truncate at word boundary
  const truncated = description.substring(0, maxLength)
  const lastSpace = truncated.lastIndexOf(' ')
  return truncated.substring(0, lastSpace) + '...'
}

// Generate keywords string with proper density
export function generateKeywords(keywords: string[], maxKeywords: number = 20): string {
  return keywords.slice(0, maxKeywords).join(', ')
}

// Generate canonical URL
export function generateCanonicalURL(path: string, baseUrl: string = 'https://oshudhigram.com'): string {
  const cleanPath = path.startsWith('/') ? path : `/${path}`
  return `${baseUrl}${cleanPath}`
}

// Generate Open Graph data
export function generateOpenGraphData(data: SEOData) {
  return {
    title: data.title,
    description: generateMetaDescription(data.description),
    url: data.url || 'https://oshudhigram.com',
    siteName: 'Oshudhigram - ঔষধি গ্রাম',
    images: [
      {
        url: data.image || 'https://oshudhigram.com/og-image.jpg',
        width: 1200,
        height: 630,
        alt: data.title,
      },
    ],
    locale: 'bn_BD',
    type: data.type || 'website',
  }
}

// Generate Twitter Card data
export function generateTwitterCardData(data: SEOData) {
  return {
    card: 'summary_large_image',
    title: generateSEOTitle(data.title),
    description: generateMetaDescription(data.description),
    images: [data.image || 'https://oshudhigram.com/twitter-image.jpg'],
    creator: '@oshudhigram',
  }
}

// Generate Product Schema
export function generateProductSchema(data: SEOData & { 
  sku?: string
  gtin?: string
  mpn?: string
  condition?: string
  reviews?: any[]
}) {
  return {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": data.title,
    "description": data.description,
    "image": data.image,
    "sku": data.sku,
    "gtin": data.gtin,
    "mpn": data.mpn,
    "brand": {
      "@type": "Brand",
      "name": data.brand || "Oshudhigram"
    },
    "category": data.category,
    "offers": {
      "@type": "Offer",
      "price": data.price,
      "priceCurrency": data.currency || "BDT",
      "availability": `https://schema.org/${data.availability || 'InStock'}`,
      "url": data.url,
      "seller": {
        "@type": "Organization",
        "name": "Oshudhigram - ঔষধি গ্রাম"
      },
      "priceValidUntil": new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    },
    "aggregateRating": data.reviews && data.reviews.length > 0 ? {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "reviewCount": data.reviews.length.toString(),
      "bestRating": "5",
      "worstRating": "1"
    } : undefined,
    "review": data.reviews?.slice(0, 5).map((review, index) => ({
      "@type": "Review",
      "author": {
        "@type": "Person",
        "name": review.author || `Customer ${index + 1}`
      },
      "reviewRating": {
        "@type": "Rating",
        "ratingValue": review.rating || "5",
        "bestRating": "5"
      },
      "reviewBody": review.comment
    }))
  }
}

// Generate Article Schema
export function generateArticleSchema(data: SEOData & {
  wordCount?: number
  section?: string
}) {
  return {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": data.title,
    "description": data.description,
    "image": data.image,
    "author": {
      "@type": "Person",
      "name": data.author || "Oshudhigram Team",
      "url": "https://oshudhigram.com/authors"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Oshudhigram - ঔষধি গ্রাম",
      "logo": {
        "@type": "ImageObject",
        "url": "https://oshudhigram.com/logo.png"
      }
    },
    "datePublished": data.publishedTime,
    "dateModified": data.modifiedTime || data.publishedTime,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": data.url
    },
    "articleSection": data.section || "Health",
    "keywords": data.keywords.join(', '),
    "wordCount": data.wordCount,
    "inLanguage": "bn-BD"
  }
}

// Generate FAQ Schema
export function generateFAQSchema(faqs: Array<{ question: string; answer: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  }
}

// Generate Breadcrumb Schema
export function generateBreadcrumbSchema(breadcrumbs: Array<{ name: string; url: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.name,
      "item": crumb.url
    }))
  }
}

// Generate Local Business Schema
export function generateLocalBusinessSchema() {
  return {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "Oshudhigram - ঔষধি গ্রাম",
    "description": "নাটোরের বিখ্যাত ঔষধি গ্রাম। ৩০০+ প্রজাতির ভেষজ গাছ ও প্রাকৃতিক পণ্য।",
    "url": "https://oshudhigram.com",
    "telephone": "+880-1700-000000",
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Herbal Village",
      "addressLocality": "Natore",
      "addressRegion": "Rajshahi",
      "postalCode": "6400",
      "addressCountry": "BD"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": 24.4206,
      "longitude": 88.9318
    },
    "openingHoursSpecification": [
      {
        "@type": "OpeningHoursSpecification",
        "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
        "opens": "08:00",
        "closes": "20:00"
      }
    ],
    "priceRange": "৳৳",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "reviewCount": "2500"
    }
  }
}

// SEO Performance Tracking
export function trackSEOEvent(eventName: string, parameters: Record<string, any> = {}) {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, {
      event_category: 'SEO',
      event_label: eventName,
      ...parameters
    })
  }
}

// Generate hreflang tags for multilingual SEO
export function generateHreflangTags(currentPath: string) {
  const baseUrl = 'https://oshudhigram.com'
  
  return [
    {
      hrefLang: 'bn-BD',
      href: `${baseUrl}${currentPath}`
    },
    {
      hrefLang: 'en-US',
      href: `${baseUrl}/en${currentPath}`
    },
    {
      hrefLang: 'x-default',
      href: `${baseUrl}${currentPath}`
    }
  ]
}

// Validate SEO data
export function validateSEOData(data: SEOData): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  
  if (!data.title || data.title.length < 10) {
    errors.push('Title should be at least 10 characters long')
  }
  
  if (data.title && data.title.length > 60) {
    errors.push('Title should be less than 60 characters')
  }
  
  if (!data.description || data.description.length < 50) {
    errors.push('Description should be at least 50 characters long')
  }
  
  if (data.description && data.description.length > 160) {
    errors.push('Description should be less than 160 characters')
  }
  
  if (!data.keywords || data.keywords.length === 0) {
    errors.push('At least one keyword is required')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { ShoppingBag, Menu, X, User, LogOut, Settings, Shield, ShoppingCart } from "lucide-react"
import { useState } from "react"
import { useAuth } from "@/contexts/AuthContext"
import { useCart } from "@/contexts/CartContext"
import Image from "next/image"

export default function Navigation() {
  const pathname = usePathname()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)
  const { user, userRole, signIn, signOut, loading } = useAuth()
  const { getTotalItems } = useCart()

  const navLinks = [
    { href: "/", label: "Home" },
    { href: "/shop", label: "Shop" },
  ]

  const handleSignIn = async () => {
    try {
      await signIn()
    } catch (error) {
      console.error("Sign in failed:", error)
    }
  }

  const handleSignOut = async () => {
    try {
      await signOut()
      setIsUserMenuOpen(false)
    } catch (error) {
      console.error("Sign out failed:", error)
    }
  }

  const getRoleBadges = () => {
    if (!userRole) return null

    const badges = []
    if (userRole.isAdmin) badges.push({ label: "Admin", color: "bg-red-100 text-red-800" })
    if (userRole.isSeller) badges.push({ label: "Seller", color: "bg-green-100 text-green-800" })
    if (userRole.isMod) badges.push({ label: "Mod", color: "bg-blue-100 text-blue-800" })

    return badges
  }

  const totalItems = getTotalItems()

  return (
    <nav className="bg-white shadow-lg sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <ShoppingBag className="h-8 w-8 text-blue-600" />
            <span className="text-xl font-bold text-gray-900">ShopHub</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className={`text-sm font-medium transition-colors duration-200 ${
                  pathname === link.href
                    ? "text-blue-600 border-b-2 border-blue-600 pb-1"
                    : "text-gray-700 hover:text-blue-600"
                }`}
              >
                {link.label}
              </Link>
            ))}

            {/* Cart Icon */}
            <Link href="/cart" className="relative text-gray-700 hover:text-blue-600">
              <ShoppingCart className="h-6 w-6" />
              {totalItems > 0 && (
                <span className="absolute -top-2 -right-2 bg-blue-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {totalItems}
                </span>
              )}
            </Link>

            {/* Auth Section */}
            {loading ? (
              <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
            ) : user ? (
              <div className="relative">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center space-x-2 text-gray-700 hover:text-blue-600 focus:outline-none"
                >
                  {user.photoURL ? (
                    <Image
                      src={user.photoURL || "/placeholder.svg"}
                      alt={user.displayName || "User"}
                      width={32}
                      height={32}
                      className="rounded-full"
                    />
                  ) : (
                    <User className="h-8 w-8 p-1 bg-gray-200 rounded-full" />
                  )}
                  <span className="text-sm font-medium">{user.displayName}</span>
                  {userRole?.isActive && <div className="w-2 h-2 bg-green-500 rounded-full"></div>}
                </button>

                {/* User Dropdown */}
                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-2">
                    <div className="px-4 py-3 border-b border-gray-200">
                      <div className="flex items-center space-x-3">
                        {user.photoURL ? (
                          <Image
                            src={user.photoURL || "/placeholder.svg"}
                            alt={user.displayName || "User"}
                            width={40}
                            height={40}
                            className="rounded-full"
                          />
                        ) : (
                          <User className="h-10 w-10 p-2 bg-gray-200 rounded-full" />
                        )}
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">{user.displayName}</p>
                          <p className="text-xs text-gray-500">{user.email}</p>
                          <div className="flex items-center space-x-1 mt-1">
                            <div
                              className={`w-2 h-2 rounded-full ${userRole?.isActive ? "bg-green-500" : "bg-gray-400"}`}
                            ></div>
                            <span className="text-xs text-gray-500">{userRole?.isActive ? "Active" : "Inactive"}</span>
                          </div>
                        </div>
                      </div>

                      {/* Role Badges */}
                      {getRoleBadges() && getRoleBadges()!.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {getRoleBadges()!.map((badge, index) => (
                            <span key={index} className={`px-2 py-1 text-xs font-medium rounded-full ${badge.color}`}>
                              {badge.label}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>

                    <div className="py-1">
                      <Link
                        href="/profile"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <User className="h-4 w-4 mr-3" />
                        Profile
                      </Link>

                      <Link
                        href="/orders"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <ShoppingBag className="h-4 w-4 mr-3" />
                        My Orders
                      </Link>

                      {userRole?.isAdmin && (
                        <Link
                          href="/admin"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => setIsUserMenuOpen(false)}
                        >
                          <Shield className="h-4 w-4 mr-3" />
                          Admin Panel
                        </Link>
                      )}

                      <Link
                        href="/settings"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <Settings className="h-4 w-4 mr-3" />
                        Settings
                      </Link>

                      <button
                        onClick={handleSignOut}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <LogOut className="h-4 w-4 mr-3" />
                        Sign Out
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <button
                onClick={handleSignIn}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
              >
                Sign In with Google
              </button>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center space-x-4">
            {/* Mobile Cart */}
            <Link href="/cart" className="relative text-gray-700 hover:text-blue-600">
              <ShoppingCart className="h-6 w-6" />
              {totalItems > 0 && (
                <span className="absolute -top-2 -right-2 bg-blue-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {totalItems}
                </span>
              )}
            </Link>

            {!loading && user && (
              <div className="flex items-center space-x-2">
                {user.photoURL ? (
                  <Image
                    src={user.photoURL || "/placeholder.svg"}
                    alt={user.displayName || "User"}
                    width={32}
                    height={32}
                    className="rounded-full"
                  />
                ) : (
                  <User className="h-8 w-8 p-1 bg-gray-200 rounded-full" />
                )}
                {userRole?.isActive && <div className="w-2 h-2 bg-green-500 rounded-full"></div>}
              </div>
            )}

            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-700 hover:text-blue-600 focus:outline-none"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className={`block px-3 py-2 text-base font-medium transition-colors duration-200 ${
                    pathname === link.href
                      ? "text-blue-600 bg-blue-50"
                      : "text-gray-700 hover:text-blue-600 hover:bg-gray-50"
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {link.label}
                </Link>
              ))}

              {/* Mobile Auth */}
              {!loading && (
                <div className="border-t border-gray-200 pt-3 mt-3">
                  {user ? (
                    <div className="space-y-2">
                      <div className="px-3 py-2">
                        <div className="flex items-center space-x-3">
                          {user.photoURL ? (
                            <Image
                              src={user.photoURL || "/placeholder.svg"}
                              alt={user.displayName || "User"}
                              width={40}
                              height={40}
                              className="rounded-full"
                            />
                          ) : (
                            <User className="h-10 w-10 p-2 bg-gray-200 rounded-full" />
                          )}
                          <div>
                            <p className="text-sm font-medium text-gray-900">{user.displayName}</p>
                            <p className="text-xs text-gray-500">{user.email}</p>
                            <div className="flex items-center space-x-1 mt-1">
                              <div
                                className={`w-2 h-2 rounded-full ${userRole?.isActive ? "bg-green-500" : "bg-gray-400"}`}
                              ></div>
                              <span className="text-xs text-gray-500">
                                {userRole?.isActive ? "Active" : "Inactive"}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Mobile Role Badges */}
                        {getRoleBadges() && getRoleBadges()!.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {getRoleBadges()!.map((badge, index) => (
                              <span key={index} className={`px-2 py-1 text-xs font-medium rounded-full ${badge.color}`}>
                                {badge.label}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>

                      <Link
                        href="/profile"
                        className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Profile
                      </Link>

                      <Link
                        href="/orders"
                        className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        My Orders
                      </Link>

                      {userRole?.isAdmin && (
                        <Link
                          href="/admin"
                          className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          Admin Panel
                        </Link>
                      )}

                      <button
                        onClick={handleSignOut}
                        className="block w-full text-left px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50"
                      >
                        Sign Out
                      </button>
                    </div>
                  ) : (
                    <button
                      onClick={handleSignIn}
                      className="block w-full text-left px-3 py-2 text-base font-medium bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                    >
                      Sign In with Google
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}

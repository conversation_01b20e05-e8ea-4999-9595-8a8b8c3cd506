import { collection, getDocs, doc, getDoc } from "firebase/firestore"
import { db, isFirebaseConfigured } from "./firebase"
import type { Product, ProductReview } from "./types"

// Helper function to convert Firestore data to plain objects
function convertFirestoreData(data: any): any {
  const converted = { ...data }
  
  // Convert Firestore Timestamps to ISO strings
  Object.keys(converted).forEach(key => {
    if (converted[key] && typeof converted[key].toDate === 'function') {
      converted[key] = converted[key].toDate().toISOString()
    }
  })
  
  return converted
}

// No hardcoded products - will return empty array when no products exist

// No hardcoded reviews - will return empty array when no reviews exist

export async function getProducts(): Promise<Product[]> {
  // Return empty array if Firebase is not configured
  if (!isFirebaseConfigured || !db) {
    console.log("Firebase not configured - returning empty products array")
    return []
  }

  try {
    const productsCollection = collection(db, "products")
    const productsSnapshot = await getDocs(productsCollection)
    const products = productsSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...convertFirestoreData(doc.data()),
    })) as Product[]

    return products
  } catch (error) {
    console.error("Error fetching products from Firestore:", error)
    return []
  }
}

export async function getProduct(id: string): Promise<Product | null> {
  // Return null if Firebase is not configured
  if (!isFirebaseConfigured || !db) {
    console.log("Firebase not configured - returning null")
    return null
  }

  try {
    const productDoc = doc(db, "products", id)
    const productSnapshot = await getDoc(productDoc)

    if (productSnapshot.exists()) {
      return {
        id: productSnapshot.id,
        ...convertFirestoreData(productSnapshot.data()),
      } as Product
    }

    return null
  } catch (error) {
    console.error("Error fetching product from Firestore:", error)
    return null
  }
}

export async function getProductReviews(productId: string): Promise<ProductReview[]> {
  // Return empty reviews array
  if (!isFirebaseConfigured) {
    return []
  }

  try {
    // In a real app, you would fetch reviews from Firestore
    // For now, return empty array
    return []
  } catch (error) {
    console.error("Error fetching reviews:", error)
    return []
  }
}

export async function getRelatedProducts(productId: string, category?: string): Promise<Product[]> {
  const allProducts = await getProducts()

  // Filter out the current product and get products from the same category
  let related = allProducts.filter(
    (product) => product.id !== productId && (category ? product.category === category : true),
  )

  // If no category match, get random products
  if (related.length === 0) {
    related = allProducts.filter((product) => product.id !== productId)
  }

  // Return up to 4 related products
  return related.slice(0, 4)
}

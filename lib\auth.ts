import {
  signInWithPopup,
  GoogleAuthProvider,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  type User,
} from "firebase/auth"
import { doc, setDoc, getDoc, updateDoc, serverTimestamp, onSnapshot } from "firebase/firestore"
import { auth, db, isFirebaseConfigured } from "./firebase"

export interface UserRole {
  id: string
  email: string
  displayName: string
  photoURL: string
  isAdmin: boolean
  isSeller: boolean
  isMod: boolean
  isActive: boolean
  lastSeen: any
  createdAt: any
  updatedAt: any
}

const googleProvider = new GoogleAuthProvider()

// Sign in with Google
export async function signInWithGoogle(): Promise<UserRole | null> {
  if (!isFirebaseConfigured || !auth) {
    console.warn("Firebase not configured")
    return null
  }

  try {
    const result = await signInWithPopup(auth, googleProvider)
    const user = result.user

    // Create or update user document
    const userRole = await createOrUpdateUser(user)
    return userRole
  } catch (error) {
    console.error("Error signing in with Google:", error)
    throw error
  }
}

// Sign out
export async function signOut(): Promise<void> {
  if (!isFirebaseConfigured || !auth) {
    console.warn("Firebase not configured")
    return
  }

  try {
    // Update user status to inactive before signing out
    if (auth.currentUser) {
      await updateUserActivity(auth.currentUser.uid, false)
    }
    await firebaseSignOut(auth)
  } catch (error) {
    console.error("Error signing out:", error)
    throw error
  }
}

// Create or update user in Firestore
async function createOrUpdateUser(user: User): Promise<UserRole> {
  if (!db) throw new Error("Firestore not initialized")

  const userRef = doc(db, "users", user.uid)
  const userSnap = await getDoc(userRef)

  const userData: Partial<UserRole> = {
    id: user.uid,
    email: user.email || "",
    displayName: user.displayName || "",
    photoURL: user.photoURL || "",
    isActive: true,
    lastSeen: serverTimestamp(),
    updatedAt: serverTimestamp(),
  }

  if (userSnap.exists()) {
    // Update existing user
    await updateDoc(userRef, userData)
    const existingData = userSnap.data() as UserRole
    return { ...existingData, ...userData } as UserRole
  } else {
    // Create new user with default roles
    const newUserData: UserRole = {
      ...userData,
      isAdmin: false,
      isSeller: false,
      isMod: false,
      createdAt: serverTimestamp(),
    } as UserRole

    await setDoc(userRef, newUserData)
    return newUserData
  }
}

// Update user activity status
export async function updateUserActivity(userId: string, isActive: boolean): Promise<void> {
  if (!db) return

  try {
    const userRef = doc(db, "users", userId)
    await updateDoc(userRef, {
      isActive,
      lastSeen: serverTimestamp(),
      updatedAt: serverTimestamp(),
    })
  } catch (error) {
    console.error("Error updating user activity:", error)
  }
}

// Get user role data
export async function getUserRole(userId: string): Promise<UserRole | null> {
  if (!db) return null

  try {
    const userRef = doc(db, "users", userId)
    const userSnap = await getDoc(userRef)

    if (userSnap.exists()) {
      return userSnap.data() as UserRole
    }
    return null
  } catch (error) {
    console.error("Error getting user role:", error)
    return null
  }
}

// Update user roles (admin only)
export async function updateUserRoles(
  userId: string,
  roles: { isAdmin?: boolean; isSeller?: boolean; isMod?: boolean },
): Promise<void> {
  if (!db) throw new Error("Firestore not initialized")

  try {
    const userRef = doc(db, "users", userId)
    await updateDoc(userRef, {
      ...roles,
      updatedAt: serverTimestamp(),
    })
  } catch (error) {
    console.error("Error updating user roles:", error)
    throw error
  }
}

// Listen to auth state changes
export function onAuthStateChange(callback: (user: User | null, userRole: UserRole | null) => void) {
  if (!isFirebaseConfigured || !auth) {
    callback(null, null)
    return () => {}
  }

  return onAuthStateChanged(auth, async (user) => {
    if (user) {
      // Update user activity when they're online
      await updateUserActivity(user.uid, true)

      // Get user role data
      const userRole = await getUserRole(user.uid)
      callback(user, userRole)
    } else {
      callback(null, null)
    }
  })
}

// Listen to user role changes
export function onUserRoleChange(userId: string, callback: (userRole: UserRole | null) => void) {
  if (!db) {
    callback(null)
    return () => {}
  }

  const userRef = doc(db, "users", userId)
  return onSnapshot(userRef, (doc) => {
    if (doc.exists()) {
      callback(doc.data() as UserRole)
    } else {
      callback(null)
    }
  })
}

"use client"

import { useAuth } from "@/contexts/AuthContext"
import { useEffect, useState } from "react"
import { collection, getDocs, doc, updateDoc, deleteDoc, addDoc, serverTimestamp } from "firebase/firestore"
import { db } from "@/lib/firebase"
import type { UserRole } from "@/lib/auth"
import type { Product } from "@/lib/types"
import { Shield, Users, Package, Settings, Trash2, Edit, Plus, Search, X, Save } from "lucide-react"
import Image from "next/image"

type TabType = "users" | "products" | "settings"

interface EditingUser extends UserRole {
  isEditing?: boolean
}

interface EditingProduct extends Product {
  isEditing?: boolean
}

export default function AdminPage() {
  const { user, userRole, loading } = useAuth()
  const [activeTab, setActiveTab] = useState<TabType>("users")
  const [users, setUsers] = useState<EditingUser[]>([])
  const [products, setProducts] = useState<EditingProduct[]>([])
  const [loadingUsers, setLoadingUsers] = useState(true)
  const [loadingProducts, setLoadingProducts] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [showAddProduct, setShowAddProduct] = useState(false)
  const [newProduct, setNewProduct] = useState<Partial<Product>>({
    name: "",
    image: "",
    price: 0,
    discountPrice: 0,
    description: "",
    category: "",
    inStock: true,
    stockQuantity: 0,
    images: [""],
  })

  useEffect(() => {
    if (userRole?.isAdmin) {
      fetchUsers()
      fetchProducts()
    }
  }, [userRole])

  const fetchUsers = async () => {
    if (!db) return

    try {
      const usersCollection = collection(db, "users")
      const usersSnapshot = await getDocs(usersCollection)
      const usersData = usersSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
        isEditing: false,
      })) as EditingUser[]

      setUsers(usersData)
    } catch (error) {
      console.error("Error fetching users:", error)
    } finally {
      setLoadingUsers(false)
    }
  }

  const fetchProducts = async () => {
    if (!db) return

    try {
      const productsCollection = collection(db, "products")
      const productsSnapshot = await getDocs(productsCollection)
      const productsData = productsSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
        isEditing: false,
      })) as EditingProduct[]

      setProducts(productsData)
    } catch (error) {
      console.error("Error fetching products:", error)
    } finally {
      setLoadingProducts(false)
    }
  }

  // User Management Functions
  const deleteUser = async (userId: string) => {
    if (!db || !confirm("Are you sure you want to delete this user?")) return

    try {
      await deleteDoc(doc(db, "users", userId))
      setUsers(users.filter((u) => u.id !== userId))
    } catch (error) {
      console.error("Error deleting user:", error)
      alert("Error deleting user")
    }
  }

  const toggleUserEdit = (userId: string) => {
    setUsers(users.map((u) => (u.id === userId ? { ...u, isEditing: !u.isEditing } : { ...u, isEditing: false })))
  }

  const updateUser = async (userId: string, updatedData: Partial<UserRole>) => {
    if (!db) return

    try {
      const userRef = doc(db, "users", userId)
      await updateDoc(userRef, {
        ...updatedData,
        updatedAt: serverTimestamp(),
      })

      setUsers(users.map((u) => (u.id === userId ? { ...u, ...updatedData, isEditing: false } : u)))
    } catch (error) {
      console.error("Error updating user:", error)
      alert("Error updating user")
    }
  }

  // Product Management Functions
  const deleteProduct = async (productId: string) => {
    if (!db || !confirm("Are you sure you want to delete this product?")) return

    try {
      await deleteDoc(doc(db, "products", productId))
      setProducts(products.filter((p) => p.id !== productId))
    } catch (error) {
      console.error("Error deleting product:", error)
      alert("Error deleting product")
    }
  }

  const toggleProductEdit = (productId: string) => {
    setProducts(
      products.map((p) => (p.id === productId ? { ...p, isEditing: !p.isEditing } : { ...p, isEditing: false })),
    )
  }

  const updateProduct = async (productId: string, updatedData: Partial<Product>) => {
    if (!db) return

    try {
      const productRef = doc(db, "products", productId)
      await updateDoc(productRef, updatedData)

      setProducts(products.map((p) => (p.id === productId ? { ...p, ...updatedData, isEditing: false } : p)))
    } catch (error) {
      console.error("Error updating product:", error)
      alert("Error updating product")
    }
  }

  const addProduct = async () => {
    if (!db || !newProduct.name || !newProduct.price || !newProduct.image || !newProduct.description) {
      alert("Please fill in all required fields (Name, Price, Main Image, Description)")
      return
    }

    try {
      const productData = {
        ...newProduct,
        price: Number(newProduct.price),
        discountPrice: newProduct.discountPrice ? Number(newProduct.discountPrice) : undefined,
        stockQuantity: Number(newProduct.stockQuantity || 0),
        images: newProduct.images?.filter(img => img.trim() !== "") || [],
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      }

      const docRef = await addDoc(collection(db, "products"), productData)

      const addedProduct = {
        id: docRef.id,
        ...productData,
        isEditing: false,
      } as EditingProduct

      setProducts([...products, addedProduct])
      setNewProduct({
        name: "",
        image: "",
        price: 0,
        discountPrice: 0,
        description: "",
        category: "",
        inStock: true,
        stockQuantity: 0,
        images: [""],
      })
      setShowAddProduct(false)
    } catch (error) {
      console.error("Error adding product:", error)
      alert("Error adding product")
    }
  }

  // Filter functions
  const filteredUsers = users.filter(
    (user) =>
      user.displayName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email?.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const filteredProducts = products.filter(
    (product) =>
      product.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description?.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600">Please sign in to access the admin panel.</p>
        </div>
      </div>
    )
  }

  if (!userRole?.isAdmin) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <Shield className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600">Only administrators can access this panel.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Admin Panel</h1>
        <p className="text-lg text-gray-600">Complete system management and control</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Users</p>
              <p className="text-2xl font-bold text-gray-900">{users.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Package className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Products</p>
              <p className="text-2xl font-bold text-gray-900">{products.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Shield className="h-8 w-8 text-red-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Users</p>
              <p className="text-2xl font-bold text-gray-900">{users.filter((u) => u.isActive).length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Settings className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Admins</p>
              <p className="text-2xl font-bold text-gray-900">{users.filter((u) => u.isAdmin).length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab("users")}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === "users"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              <Users className="h-5 w-5 inline mr-2" />
              User Management
            </button>
            <button
              onClick={() => setActiveTab("products")}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === "products"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              <Package className="h-5 w-5 inline mr-2" />
              Product Management
            </button>
            <button
              onClick={() => setActiveTab("settings")}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === "settings"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              <Settings className="h-5 w-5 inline mr-2" />
              System Settings
            </button>
          </nav>
        </div>

        {/* Search Bar */}
        {(activeTab === "users" || activeTab === "products") && (
          <div className="p-6 border-b border-gray-200">
            <div className="relative">
              <Search className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
              <input
                type="text"
                placeholder={`Search ${activeTab}...`}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        )}

        {/* Tab Content */}
        <div className="p-6">
          {/* Users Tab */}
          {activeTab === "users" && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-semibold text-gray-900">User Management</h2>
                <span className="text-sm text-gray-500">{filteredUsers.length} users found</span>
              </div>

              {loadingUsers ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          User
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Roles
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredUsers.map((userData) => (
                        <tr key={userData.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              {userData.photoURL ? (
                                <Image
                                  src={userData.photoURL || "/placeholder.svg"}
                                  alt={userData.displayName}
                                  width={40}
                                  height={40}
                                  className="rounded-full"
                                />
                              ) : (
                                <div className="h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                                  <Users className="h-5 w-5 text-gray-500" />
                                </div>
                              )}
                              <div className="ml-4">
                                {userData.isEditing ? (
                                  <div className="space-y-1">
                                    <input
                                      type="text"
                                      defaultValue={userData.displayName}
                                      className="text-sm font-medium text-gray-900 border rounded px-2 py-1"
                                      onBlur={(e) => updateUser(userData.id, { displayName: e.target.value })}
                                    />
                                    <input
                                      type="email"
                                      defaultValue={userData.email}
                                      className="text-sm text-gray-500 border rounded px-2 py-1"
                                      onBlur={(e) => updateUser(userData.id, { email: e.target.value })}
                                    />
                                  </div>
                                ) : (
                                  <div>
                                    <div className="text-sm font-medium text-gray-900">{userData.displayName}</div>
                                    <div className="text-sm text-gray-500">{userData.email}</div>
                                  </div>
                                )}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div
                                className={`w-2 h-2 rounded-full mr-2 ${userData.isActive ? "bg-green-500" : "bg-gray-400"}`}
                              ></div>
                              <span className={`text-sm ${userData.isActive ? "text-green-800" : "text-gray-500"}`}>
                                {userData.isActive ? "Active" : "Inactive"}
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {userData.isEditing ? (
                              <div className="space-y-2">
                                <label className="flex items-center">
                                  <input
                                    type="checkbox"
                                    defaultChecked={userData.isAdmin}
                                    onChange={(e) => updateUser(userData.id, { isAdmin: e.target.checked })}
                                    className="mr-2"
                                  />
                                  <span className="text-xs">Admin</span>
                                </label>
                                <label className="flex items-center">
                                  <input
                                    type="checkbox"
                                    defaultChecked={userData.isSeller}
                                    onChange={(e) => updateUser(userData.id, { isSeller: e.target.checked })}
                                    className="mr-2"
                                  />
                                  <span className="text-xs">Seller</span>
                                </label>
                                <label className="flex items-center">
                                  <input
                                    type="checkbox"
                                    defaultChecked={userData.isMod}
                                    onChange={(e) => updateUser(userData.id, { isMod: e.target.checked })}
                                    className="mr-2"
                                  />
                                  <span className="text-xs">Mod</span>
                                </label>
                              </div>
                            ) : (
                              <div className="flex flex-wrap gap-1">
                                {userData.isAdmin && (
                                  <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                                    Admin
                                  </span>
                                )}
                                {userData.isSeller && (
                                  <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                    Seller
                                  </span>
                                )}
                                {userData.isMod && (
                                  <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                    Mod
                                  </span>
                                )}
                                {!userData.isAdmin && !userData.isSeller && !userData.isMod && (
                                  <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                    User
                                  </span>
                                )}
                              </div>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            {userData.id !== user.uid && (
                              <div className="flex space-x-2">
                                <button
                                  onClick={() => toggleUserEdit(userData.id)}
                                  className="text-blue-600 hover:text-blue-900"
                                >
                                  {userData.isEditing ? <Save className="h-4 w-4" /> : <Edit className="h-4 w-4" />}
                                </button>
                                <button
                                  onClick={() => deleteUser(userData.id)}
                                  className="text-red-600 hover:text-red-900"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </div>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}

          {/* Products Tab */}
          {activeTab === "products" && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-semibold text-gray-900">Product Management</h2>
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-500">{filteredProducts.length} products found</span>
                  <button
                    onClick={() => setShowAddProduct(true)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Product
                  </button>
                </div>
              </div>

              {/* Add Product Modal */}
              {showAddProduct && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                  <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-semibold">Add New Product</h3>
                      <button onClick={() => setShowAddProduct(false)}>
                        <X className="h-5 w-5" />
                      </button>
                    </div>
                    <div className="space-y-4">
                      {/* Basic Information */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Product Name *</label>
                          <input
                            type="text"
                            placeholder="Enter product name"
                            value={newProduct.name}
                            onChange={(e) => setNewProduct({ ...newProduct, name: e.target.value })}
                            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                          <input
                            type="text"
                            placeholder="e.g., Electronics, Clothing"
                            value={newProduct.category}
                            onChange={(e) => setNewProduct({ ...newProduct, category: e.target.value })}
                            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Stock Quantity</label>
                        <input
                          type="number"
                          placeholder="0"
                          min="0"
                          value={newProduct.stockQuantity}
                          onChange={(e) => setNewProduct({ ...newProduct, stockQuantity: Number(e.target.value) })}
                          className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>

                      {/* Pricing */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Regular Price *</label>
                          <input
                            type="number"
                            placeholder="0.00"
                            min="0"
                            step="0.01"
                            value={newProduct.price}
                            onChange={(e) => setNewProduct({ ...newProduct, price: Number(e.target.value) })}
                            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Discount Price</label>
                          <input
                            type="number"
                            placeholder="0.00 (optional)"
                            min="0"
                            step="0.01"
                            value={newProduct.discountPrice}
                            onChange={(e) => setNewProduct({ ...newProduct, discountPrice: Number(e.target.value) })}
                            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                      </div>

                      {/* Stock Status */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Stock Status</label>
                        <div className="flex items-center space-x-4">
                          <label className="flex items-center">
                            <input
                              type="radio"
                              name="inStock"
                              checked={newProduct.inStock === true}
                              onChange={() => setNewProduct({ ...newProduct, inStock: true })}
                              className="mr-2"
                            />
                            In Stock
                          </label>
                          <label className="flex items-center">
                            <input
                              type="radio"
                              name="inStock"
                              checked={newProduct.inStock === false}
                              onChange={() => setNewProduct({ ...newProduct, inStock: false })}
                              className="mr-2"
                            />
                            Out of Stock
                          </label>
                        </div>
                      </div>

                      {/* Main Image */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Main Image URL *</label>
                        <input
                          type="url"
                          placeholder="https://example.com/image.jpg"
                          value={newProduct.image}
                          onChange={(e) => setNewProduct({ ...newProduct, image: e.target.value })}
                          className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>

                      {/* Additional Images */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Additional Images</label>
                        {newProduct.images?.map((imageUrl, index) => (
                          <div key={index} className="flex items-center space-x-2 mb-2">
                            <input
                              type="url"
                              placeholder={`Image URL ${index + 1}`}
                              value={imageUrl}
                              onChange={(e) => {
                                const newImages = [...(newProduct.images || [])]
                                newImages[index] = e.target.value
                                setNewProduct({ ...newProduct, images: newImages })
                              }}
                              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                            {newProduct.images && newProduct.images.length > 1 && (
                              <button
                                type="button"
                                onClick={() => {
                                  const newImages = newProduct.images?.filter((_, i) => i !== index)
                                  setNewProduct({ ...newProduct, images: newImages })
                                }}
                                className="text-red-600 hover:text-red-800"
                              >
                                <X className="h-4 w-4" />
                              </button>
                            )}
                          </div>
                        ))}
                        <button
                          type="button"
                          onClick={() => {
                            const newImages = [...(newProduct.images || []), ""]
                            setNewProduct({ ...newProduct, images: newImages })
                          }}
                          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                        >
                          + Add Another Image
                        </button>
                      </div>

                      {/* Description */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Description *</label>
                        <textarea
                          placeholder="Enter product description"
                          value={newProduct.description}
                          onChange={(e) => setNewProduct({ ...newProduct, description: e.target.value })}
                          className="w-full border border-gray-300 rounded-lg px-3 py-2 h-24 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                      <div className="flex space-x-3">
                        <button
                          onClick={addProduct}
                          className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg"
                        >
                          Add Product
                        </button>
                        <button
                          onClick={() => setShowAddProduct(false)}
                          className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 rounded-lg"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {loadingProducts ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredProducts.map((product) => (
                    <div key={product.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                      <div className="relative h-48">
                        <Image
                          src={product.image || "/placeholder.svg?height=192&width=256&query=product"}
                          alt={product.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="p-4">
                        {product.isEditing ? (
                          <div className="space-y-3">
                            <input
                              type="text"
                              defaultValue={product.name}
                              className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
                              onBlur={(e) => updateProduct(product.id, { name: e.target.value })}
                            />
                            <input
                              type="url"
                              defaultValue={product.image}
                              className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
                              onBlur={(e) => updateProduct(product.id, { image: e.target.value })}
                            />
                            <input
                              type="number"
                              defaultValue={product.price}
                              className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
                              onBlur={(e) => updateProduct(product.id, { price: Number(e.target.value) })}
                            />
                            <textarea
                              defaultValue={product.description}
                              className="w-full border border-gray-300 rounded px-2 py-1 text-sm h-20"
                              onBlur={(e) => updateProduct(product.id, { description: e.target.value })}
                            />
                          </div>
                        ) : (
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">{product.name}</h3>
                            {product.category && (
                              <span className="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full mb-2">
                                {product.category}
                              </span>
                            )}
                            <p className="text-gray-600 text-sm mb-3 line-clamp-2">{product.description}</p>
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center space-x-2">
                                {product.discountPrice && product.discountPrice > 0 ? (
                                  <>
                                    <span className="text-lg font-bold text-red-600">${product.discountPrice.toFixed(2)}</span>
                                    <span className="text-sm text-gray-500 line-through">${product.price.toFixed(2)}</span>
                                  </>
                                ) : (
                                  <span className="text-lg font-bold text-blue-600">${product.price.toFixed(2)}</span>
                                )}
                              </div>
                              <span className={`text-xs px-2 py-1 rounded-full ${
                                product.inStock ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                              }`}>
                                {product.inStock ? 'In Stock' : 'Out of Stock'}
                              </span>
                            </div>
                            {product.stockQuantity !== undefined && (
                              <p className="text-xs text-gray-500">Stock: {product.stockQuantity} units</p>
                            )}
                            {product.images && product.images.length > 0 && (
                              <p className="text-xs text-gray-500 mt-1">{product.images.length + 1} images</p>
                            )}
                          </div>
                        )}

                        <div className="flex justify-between items-center mt-4">
                          <button
                            onClick={() => toggleProductEdit(product.id)}
                            className="text-blue-600 hover:text-blue-900 flex items-center"
                          >
                            {product.isEditing ? <Save className="h-4 w-4 mr-1" /> : <Edit className="h-4 w-4 mr-1" />}
                            {product.isEditing ? "Save" : "Edit"}
                          </button>
                          <button
                            onClick={() => deleteProduct(product.id)}
                            className="text-red-600 hover:text-red-900 flex items-center"
                          >
                            <Trash2 className="h-4 w-4 mr-1" />
                            Delete
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Settings Tab */}
          {activeTab === "settings" && (
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-6">System Settings</h2>
              <div className="space-y-6">
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-md font-medium text-gray-900 mb-4">Database Statistics</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Total Users</p>
                      <p className="text-2xl font-bold text-gray-900">{users.length}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Total Products</p>
                      <p className="text-2xl font-bold text-gray-900">{products.length}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Active Users</p>
                      <p className="text-2xl font-bold text-green-600">{users.filter((u) => u.isActive).length}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Admin Users</p>
                      <p className="text-2xl font-bold text-red-600">{users.filter((u) => u.isAdmin).length}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 rounded-lg p-6">
                  <h3 className="text-md font-medium text-gray-900 mb-4">Admin Actions</h3>
                  <div className="space-y-3">
                    <button
                      onClick={fetchUsers}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg text-left"
                    >
                      Refresh User Data
                    </button>
                    <button
                      onClick={fetchProducts}
                      className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg text-left"
                    >
                      Refresh Product Data
                    </button>
                  </div>
                </div>

                <div className="bg-yellow-50 rounded-lg p-6">
                  <h3 className="text-md font-medium text-gray-900 mb-4">System Information</h3>
                  <div className="space-y-2 text-sm">
                    <p>
                      <strong>Admin User:</strong> {user.displayName} ({user.email})
                    </p>
                    <p>
                      <strong>Last Login:</strong> {new Date().toLocaleString()}
                    </p>
                    <p>
                      <strong>System Status:</strong> <span className="text-green-600">Online</span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export const dynamic = 'force-static';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Star, Quote, MapPin, Calendar, ThumbsUp } from "lucide-react";

export default function CustomerTestimonials() {
  const testimonials = [
    {
      name: "ডাঃ আব্দুল করিম",
      location: "ঢাকা মেডিকেল কলেজ",
      role: "চিকিৎসক",
      rating: 5,
      date: "২০২৪",
      review: "আমি আমার রোগীদের ঔষধি গ্রামের প্রাকৃতিক ওষুধ সুপারিশ করি। এগুলো সত্যিই কার্যকর এবং কোনো পার্শ্বপ্রতিক্রিয়া নেই। বিশেষ করে ডায়াবেটিস ও হৃদরোগের জন্য অত্যন্ত উপকারী।",
      avatar: "ড",
      bgColor: "bg-blue-500",
      verified: true,
      category: "চিকিৎসক"
    },
    {
      name: "সালমা খাতুন",
      location: "চট্টগ্রাম",
      role: "গৃহিণী",
      rating: 5,
      date: "২০২৪",
      review: "৩ বছর ধরে ব্যবহার করছি। আমার ডায়াবেটিস নিয়ন্ত্রণে আছে এবং রক্তচাপও স্বাভাবিক। পরিবারের সবাই এখন প্রাকৃতিক ওষুধ ব্যবহার করি। দাম সাশ্রয়ী এবং গুণগত মান চমৎকার।",
      avatar: "স",
      bgColor: "bg-pink-500",
      verified: true,
      category: "নিয়মিত ক্রেতা"
    },
    {
      name: "মোঃ রফিকুল ইসলাম",
      location: "সিলেট",
      role: "ব্যবসায়ী",
      rating: 5,
      date: "২০২৩",
      review: "আমার পেটের সমস্যা ছিল দীর্ঘদিন। ডাক্তারের ওষুধে কাজ হচ্ছিল না। ঔষধি গ্রামের ত্রিফলা ও আমলকীর চূর্ণ খেয়ে সম্পূর্ণ সুস্থ হয়েছি। এখন নিয়মিত অর্ডার করি।",
      avatar: "র",
      bgColor: "bg-green-500",
      verified: true,
      category: "সন্তুষ্ট ক্রেতা"
    },
    {
      name: "ফাতেমা বেগম",
      location: "রাজশাহী",
      role: "শিক্ষিকা",
      rating: 5,
      date: "২০২৪",
      review: "আমার চুল পড়ার সমস্যা ছিল। ঔষধি গ্রামের নিম ও অ্যালোভেরার তেল ব্যবহার করে চুল ঘন হয়েছে। ত্বকও উজ্জ্বল হয়েছে। সবার কাছে সুপারিশ করি।",
      avatar: "ফ",
      bgColor: "bg-purple-500",
      verified: true,
      category: "সৌন্দর্য পণ্য"
    },
    {
      name: "আলহাজ্ব মতিউর রহমান",
      location: "বরিশাল",
      role: "অবসরপ্রাপ্ত সরকারি কর্মকর্তা",
      rating: 5,
      date: "২০২৩",
      review: "বয়সের কারণে নানা সমস্যা ছিল। জয়েন্টের ব্যথা, ঘুমের সমস্যা। অশ্বগন্ধা ও শতাবরী খেয়ে অনেক উপকার পেয়েছি। এখন আগের মতো সুস্থ বোধ করি।",
      avatar: "ম",
      bgColor: "bg-orange-500",
      verified: true,
      category: "বয়স্ক ক্রেতা"
    },
    {
      name: "নাসির উদ্দিন আহমেদ",
      location: "খুলনা",
      role: "প্রকৌশলী",
      rating: 5,
      date: "২০২৪",
      review: "কাজের চাপে মানসিক অবসাদ ছিল। ব্রাহ্মী ও শঙ্খপুষ্পী খেয়ে মন শান্ত হয়েছে। ঘুম ভালো হয় এবং কাজে মনোযোগ বেড়েছে। অফিসের সবাইকে বলেছি।",
      avatar: "ন",
      bgColor: "bg-indigo-500",
      verified: true,
      category: "মানসিক স্বাস্থ্য"
    }
  ];

  const stats = [
    { label: "সন্তুষ্ট গ্রাহক", value: "৫০,০০০+", icon: <ThumbsUp className="h-5 w-5" /> },
    { label: "গড় রেটিং", value: "৪.৯/৫", icon: <Star className="h-5 w-5" /> },
    { label: "পুনরায় অর্ডার", value: "৯৫%", icon: <Calendar className="h-5 w-5" /> },
    { label: "সুপারিশ হার", value: "৯৮%", icon: <Quote className="h-5 w-5" /> }
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-green-50/30 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <Badge
            className="mb-4 bg-[#10c255]/10 text-[#10c255] border-[#10c255]/20"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            গ্রাহক পর্যালোচনা
          </Badge>
          <h2
            className="text-4xl font-bold text-gray-900 mb-4"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            আমাদের সন্তুষ্ট গ্রাহকদের মতামত
          </h2>
          <p
            className="text-lg text-gray-600 max-w-3xl mx-auto"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            হাজারো গ্রাহকের বিশ্বাস ও ভালোবাসায় আমরা এগিয়ে চলেছি
          </p>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => (
            <Card key={index} className="backdrop-blur-sm bg-white/80 border-[#10c255]/10 text-center">
              <CardContent className="p-6">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <div className="text-[#10c255]">{stat.icon}</div>
                  <div
                    className="text-2xl font-bold text-[#10c255]"
                    style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                  >
                    {stat.value}
                  </div>
                </div>
                <p
                  className="text-sm text-gray-600"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  {stat.label}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card
              key={index}
              className="group hover:shadow-xl transition-all duration-300 backdrop-blur-sm bg-white/80 border-[#10c255]/10 hover:border-[#10c255]/30"
            >
              <CardContent className="p-6">
                {/* Header */}
                <div className="flex items-start gap-4 mb-4">
                  <div className={`w-12 h-12 rounded-full ${testimonial.bgColor} flex items-center justify-center text-white font-bold text-lg`}>
                    {testimonial.avatar}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4
                        className="font-semibold text-gray-900"
                        style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                      >
                        {testimonial.name}
                      </h4>
                      {testimonial.verified && (
                        <Badge className="bg-green-100 text-green-700 text-xs px-2 py-0.5">
                          ✓ যাচাইকৃত
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-1 text-xs text-gray-500 mb-2">
                      <MapPin className="h-3 w-3" />
                      <span style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}>
                        {testimonial.location}
                      </span>
                    </div>
                    <p
                      className="text-xs text-gray-600"
                      style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                    >
                      {testimonial.role}
                    </p>
                  </div>
                </div>

                {/* Rating */}
                <div className="flex items-center gap-1 mb-3">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                  ))}
                  <span className="text-sm text-gray-500 ml-2">{testimonial.date}</span>
                </div>

                {/* Review */}
                <div className="relative">
                  <Quote className="h-6 w-6 text-[#10c255]/20 absolute -top-2 -left-1" />
                  <p
                    className="text-gray-700 text-sm leading-relaxed pl-4"
                    style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                  >
                    {testimonial.review}
                  </p>
                </div>

                {/* Category Badge */}
                <div className="mt-4">
                  <Badge className="bg-[#10c255]/10 text-[#10c255] text-xs">
                    <span style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}>
                      {testimonial.category}
                    </span>
                  </Badge>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <Card className="backdrop-blur-sm bg-gradient-to-r from-[#10c255]/10 to-green-100/50 border-[#10c255]/20">
            <CardContent className="p-8">
              <h3
                className="text-2xl font-bold text-gray-900 mb-4"
                style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
              >
                আপনিও আমাদের সন্তুষ্ট গ্রাহক হন
              </h3>
              <p
                className="text-gray-600 mb-6 max-w-2xl mx-auto"
                style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
              >
                আজই অর্ডার করুন এবং প্রাকৃতিক ভেষজ চিকিৎসার উপকারিতা অনুভব করুন। 
                আমাদের ১০০% প্রাকৃতিক পণ্যে আস্থা রাখুন।
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Badge className="bg-[#10c255] text-white px-6 py-2 text-sm">
                  <span style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}>
                    ৩০ দিনের মানি ব্যাক গ্যারান্টি
                  </span>
                </Badge>
                <Badge className="bg-blue-500 text-white px-6 py-2 text-sm">
                  <span style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}>
                    ফ্রি হোম ডেলিভারি
                  </span>
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

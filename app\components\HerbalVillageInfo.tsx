export const dynamic = 'force-static'; 
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPin, Users, Leaf, Home } from "lucide-react";

export default function HerbalVillageInfo() {
  return (
    <section className="py-20 bg-gradient-to-b from-green-50/50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <Badge
            className="mb-4 bg-[#10c255]/10 text-[#10c255] border-[#10c255]/20"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            ঔষধি গ্রাম সম্পর্কে
          </Badge>
          <h2
            className="text-4xl font-bold text-gray-900 mb-4"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            লক্ষ্মীপুর খোলাবাড়ীয়া ঔষধি গ্রাম
          </h2>
          <p
            className="text-lg text-gray-600 max-w-3xl mx-auto"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            নাটোর সদর থেকে ১০ কিলোমিটার পূর্বে ঢাকা রাজশাহী মহাসড়কের পার্শ্বে অবস্থিত
            প্রায় ১৫টি গ্রাম নিয়ে গঠিত বাংলাদেশের অন্যতম ঔষধি গ্রাম
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          <Card className="backdrop-blur-sm bg-white/80 border-[#10c255]/10">
            <CardContent className="p-8">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-12 h-12 rounded-full bg-[#10c255]/10 flex items-center justify-center">
                  <MapPin className="h-6 w-6 text-[#10c255]" />
                </div>
                <h3
                  className="text-2xl font-semibold"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  অবস্থান ও এলাকা
                </h3>
              </div>
              <div className="space-y-4 text-gray-600">
                <p style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}>
                  <strong>আয়তন:</strong> ১২.৭৪ বর্গ কিলোমিটার
                </p>
                <p style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}>
                  <strong>জেলা পরিষদ থেকে দূরত্ব:</strong> ৮ কিলোমিটার
                </p>
                <p style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}>
                  <strong>উপজেলা পরিষদ থেকে দূরত্ব:</strong> ৮ কিলোমিটার
                </p>
                <p style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}>
                  <strong>মোট গ্রাম:</strong> ১৪টি গ্রাম
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="backdrop-blur-sm bg-white/80 border-[#10c255]/10">
            <CardContent className="p-8">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-12 h-12 rounded-full bg-[#10c255]/10 flex items-center justify-center">
                  <Users className="h-6 w-6 text-[#10c255]" />
                </div>
                <h3
                  className="text-2xl font-semibold"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  জনসংখ্যা ও সুবিধা
                </h3>
              </div>
              <div className="space-y-4 text-gray-600">
                <p style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}>
                  <strong>মোট জনসংখ্যা:</strong> ৪৫,৫৫০ জন
                </p>
                <p style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}>
                  <strong>মোট ভোটার:</strong> ৩০,২০১ জন
                </p>
                <p style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}>
                  <strong>হাট-বাজার:</strong> ৬টি
                </p>
                <p style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}>
                  <strong>ডাকঘর:</strong> ৩টি
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card className="backdrop-blur-sm bg-white/80 border-[#10c255]/10">
          <CardContent className="p-8">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-12 h-12 rounded-full bg-[#10c255]/10 flex items-center justify-center">
                <Leaf className="h-6 w-6 text-[#10c255]" />
              </div>
              <h3
                className="text-2xl font-semibold"
                style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
              >
                ঔষধি চাষাবাদ
              </h3>
            </div>
            <p
              className="text-gray-600 leading-relaxed mb-6"
              style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
            >
              এসব গ্রামের কৃষকেরা সমিতির মাধ্যমে (খোলাবাড়ীয়া ভেষজ ঔষধী গ্রাম সংগঠন) 
              অধিকাংশ কৃষিজমি ও বাড়ির আঙ্গিনার আশেপাশে ব্যাপক হারে বিভিন্ন প্রকারের 
              প্রায় ৩০০ প্রজাতির ঔষধীজাত গাছ গাছড়ার চাষাবাদ করে স্থানীয় চাহিদা মিটিয়ে 
              প্রতিদিন দেশের বিভিন্ন স্থানে বাণিজ্যিকভাবে বাজারজাত করে থাকে।
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-[#10c255]/5 rounded-lg">
                <p
                  className="text-2xl font-bold text-[#10c255]"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  ৩৫০
                </p>
                <p
                  className="text-sm text-gray-600"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  একর জমি
                </p>
              </div>
              <div className="text-center p-4 bg-[#10c255]/5 rounded-lg">
                <p
                  className="text-2xl font-bold text-[#10c255]"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  ৩০০+
                </p>
                <p
                  className="text-sm text-gray-600"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  প্রজাতির গাছ
                </p>
              </div>
              <div className="text-center p-4 bg-[#10c255]/5 rounded-lg">
                <p
                  className="text-2xl font-bold text-[#10c255]"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  ১৫
                </p>
                <p
                  className="text-sm text-gray-600"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  টি গ্রাম
                </p>
              </div>
              <div className="text-center p-4 bg-[#10c255]/5 rounded-lg">
                <p
                  className="text-2xl font-bold text-[#10c255]"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  ১০০%
                </p>
                <p
                  className="text-sm text-gray-600"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  প্রাকৃতিক
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
}
"use client"

import type React from "react"
import { useAuth } from "@/contexts/AuthContext"
import { useCart } from "@/contexts/CartContext"
import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { Lock, ArrowLeft, Check, User } from "lucide-react"
import Link from "next/link"
import { createOrder } from "@/lib/orders"
import type { ShippingInfo } from "@/lib/types"

interface ExtendedShippingInfo extends ShippingInfo {
  email?: string
}

export default function CheckoutPage() {
  const { user, signIn } = useAuth()
  const { items, getTotalPrice, clearCart } = useCart()
  const router = useRouter()

  const [shippingInfo, setShippingInfo] = useState<ExtendedShippingInfo>({
    name: "",
    phone: "",
    address: "",
    notes: "",
    email: "",
  })

  const [isProcessing, setIsProcessing] = useState(false)
  const [orderComplete, setOrderComplete] = useState(false)
  const [orderNumber, setOrderNumber] = useState<string>("")
  const [showGuestForm, setShowGuestForm] = useState(false)

  const totalPrice = getTotalPrice()
  const shipping = totalPrice > 50 ? 0 : 9.99
  const tax = 0
  const finalTotal = totalPrice + shipping + tax

  useEffect(() => {
    if (items.length === 0 && !orderComplete) {
      router.push("/cart")
    }
  }, [items.length, orderComplete, router])

  const handleSignIn = async () => {
    try {
      await signIn()
    } catch (error) {
      console.error("Sign in failed:", error)
    }
  }

  const handleOrderSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsProcessing(true)

    try {
      // Create the order
      const orderId = await createOrder(
        items,
        {
          name: shippingInfo.name,
          phone: shippingInfo.phone,
          address: shippingInfo.address,
          notes: shippingInfo.notes,
        },
        user?.uid,
        shippingInfo.email || user?.email || undefined
      )

      // Clear cart and show success
      clearCart()
      setOrderComplete(true)
      setOrderNumber(orderId)
    } catch (error) {
      console.error("Error creating order:", error)
      alert("Failed to create order. Please try again.")
    } finally {
      setIsProcessing(false)
    }
  }

  // Show guest/user choice if not authenticated and guest form not shown
  if (!user && !showGuestForm) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-md mx-auto text-center">
          <div className="mb-8">
            <Link href="/cart" className="flex items-center text-[rgb(11,193,85)] hover:text-[rgb(8,148,65)] mb-4 justify-center">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Cart
            </Link>
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Checkout Options</h1>
            <p className="text-gray-600 mb-8">Choose how you'd like to proceed with your order.</p>
          </div>

          <div className="space-y-4">
            <button
              onClick={() => setShowGuestForm(true)}
              className="w-full bg-[rgb(11,193,85)] hover:bg-[rgb(8,148,65)] text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center"
            >
              <User className="h-4 w-4 mr-2" />
              Continue as Guest
            </button>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">or</span>
              </div>
            </div>

            <button
              onClick={handleSignIn}
              className="w-full bg-white hover:bg-gray-50 text-gray-900 py-3 px-4 rounded-lg font-medium border border-gray-300 transition-colors flex items-center justify-center"
            >
              <Lock className="h-4 w-4 mr-2" />
              Sign In with Google
            </button>
          </div>
        </div>
      </div>
    )
  }

  if (orderComplete) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-md mx-auto text-center">
          <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <Check className="h-8 w-8 text-green-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Order Complete!</h1>
          <p className="text-gray-600 mb-8">
            Thank you for your purchase. Your order has been successfully processed and you'll receive a confirmation
            email shortly.
          </p>
          <div className="space-y-3">
            {user ? (
              <Link
                href="/orders"
                className="w-full bg-[rgb(11,193,85)] hover:bg-[rgb(8,148,65)] text-white py-3 px-4 rounded-lg font-medium block transition-colors"
              >
                View My Orders
              </Link>
            ) : (
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
                <p className="text-sm text-gray-600 mb-2">
                  Order placed successfully! Since you checked out as a guest, you won't be able to track this order online.
                </p>
                <p className="text-xs text-gray-500">
                  Consider creating an account for future orders to track them easily.
                </p>
              </div>
            )}
            <Link
              href="/shop"
              className="w-full bg-gray-200 hover:bg-gray-300 text-gray-800 py-3 px-4 rounded-lg font-medium block transition-colors"
            >
              Continue Shopping
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <Link href="/cart" className="flex items-center text-[rgb(11,193,85)] hover:text-[rgb(8,148,65)] mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Cart
        </Link>
        <h1 className="text-3xl font-bold text-gray-900">Checkout</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Shipping Information</h2>

            <form onSubmit={handleOrderSubmit} className="space-y-4">
              {!user && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                  <p className="text-sm text-blue-800">
                    You're checking out as a guest. You can create an account later to track your orders.
                  </p>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">নাম *</label>
                <input
                  type="text"
                  required
                  placeholder="আপনার নাম লিখুন"
                  value={shippingInfo.name}
                  onChange={(e) => setShippingInfo({ ...shippingInfo, name: e.target.value })}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>

              {!user && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email (Optional)</label>
                  <input
                    type="email"
                    placeholder="<EMAIL>"
                    value={shippingInfo.email}
                    onChange={(e) => setShippingInfo({ ...shippingInfo, email: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  />
                  <p className="text-xs text-gray-500 mt-1">We'll send order updates to this email if provided</p>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">ফোন নাম্বার *</label>
                <input
                  type="tel"
                  required
                  placeholder="আপনার মোবাইল নাম্বার লিখুন"
                  value={shippingInfo.phone}
                  onChange={(e) => setShippingInfo({ ...shippingInfo, phone: e.target.value })}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">ঠিকানা</label>
                <input
                  type="text"
                  required
                  placeholder="আপনার ঠিকানা লিখুন"
                  value={shippingInfo.address}
                  onChange={(e) => setShippingInfo({ ...shippingInfo, address: e.target.value })}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Additional information</label>
                <textarea
                  placeholder="Order notes (optional)"
                  value={shippingInfo.notes}
                  onChange={(e) => setShippingInfo({ ...shippingInfo, notes: e.target.value })}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  rows={4}
                />
              </div>

              <button
                type="submit"
                disabled={isProcessing}
                className="w-full bg-[rgb(11,193,85)] hover:bg-[rgb(8,148,65)] disabled:bg-[rgb(107,229,158)] text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center"
              >
                {isProcessing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    <Lock className="h-4 w-4 mr-2" />
                    Complete Order
                  </>
                )}
              </button>
            </form>
          </div>
        </div>

        {/* Order Summary */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-6 sticky top-24">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h2>

            <div className="space-y-3 mb-4">
              {items.map((item) => (
                <div key={item.id} className="flex items-center space-x-3">
                  <div className="relative h-12 w-12 flex-shrink-0">
                    <Image
                      src={item.image || "/placeholder.svg?height=48&width=48&query=product"}
                      alt={item.name}
                      fill
                      className="object-cover rounded"
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">{item.name}</p>
                    <p className="text-sm text-gray-500">Qty: {item.quantity}</p>
                  </div>
                  <p className="text-sm font-medium text-gray-900">${(item.price * item.quantity).toFixed(2)}</p>
                </div>
              ))}
            </div>

            <div className="border-t border-gray-200 pt-4 space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Subtotal</span>
                <span className="font-medium">${totalPrice.toFixed(2)}</span>
              </div>

              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Shipping</span>
                <span className="font-medium">
                  {shipping === 0 ? <span className="text-green-600">Free</span> : `${shipping.toFixed(2)}`}
                </span>
              </div>

              

              <div className="border-t border-gray-200 pt-2">
                <div className="flex justify-between">
                  <span className="text-lg font-semibold">Total</span>
                  <span className="text-lg font-bold text-[rgb(11,193,85)]">${finalTotal.toFixed(2)}</span>
                </div>
              </div>
            </div>

            <div className="mt-6 flex items-center justify-center text-sm text-gray-500">
              <Lock className="h-4 w-4 mr-1" />
              Secure checkout powered by ShopHub
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

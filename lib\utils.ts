import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { FieldValue } from "firebase/firestore"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatFirestoreDate(date: string | FieldValue | undefined): string {
  if (!date) return "N/A"
  if (typeof date === "string") {
    return new Date(date).toLocaleDateString()
  }
  // For FieldValue (serverTimestamp), we can't format it directly
  // This should only happen during creation before the document is saved
  return "Just now"
}

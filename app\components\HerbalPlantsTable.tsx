export const dynamic = 'force-static'; 
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Leaf, BarChart3 } from "lucide-react";

const herbalPlants = [
  { name: "এলোভেরা/ঘৃতকুমারী/ঘৃতকাঞ্চন", area: "২১০ বিঘা" },
  { name: "অর্শ্বগন্ধা", area: "১২০ বিঘা" },
  { name: "মিছরিদানা/দাউদমূল", area: "১০ বিঘা" },
  { name: "কালোমেঘ", area: "৫ বিঘা" },
  { name: "তুলসী", area: "২ বিঘা" },
  { name: "শতমূল", area: "২ বিঘা" },
  { name: "হসতি পলাশ", area: "২ বিঘা" },
  { name: "বাশক", area: "২ বিঘা" },
  { name: "শিমুল মূল", area: "৪১৪ বিঘা" },
  { name: "অন্যান্য", area: "২০০ বিঘা" },
];

const otherPlants = [
  "ঘৃতকাঞ্চন", "দুধসাগর", "পাথরকুচি", "অর্শবগন্ধা", "কাশাবা", 
  "উলুটকমল", "হসিত্মকর্ণপলাশ", "শতমূল", "বাশক", "শিমুল মূল", "দাউদ মূল"
];

export default function HerbalPlantsTable() {
  const totalArea = herbalPlants.reduce((sum, plant) => {
    const area = parseInt(plant.area.replace(/[^\d]/g, ''));
    return sum + area;
  }, 0);

  return (
    <section className="py-20 bg-gradient-to-b from-white to-green-50/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <Badge
            className="mb-4 bg-[#10c255]/10 text-[#10c255] border-[#10c255]/20"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            চাষাবাদের তথ্য
          </Badge>
          <h2
            className="text-4xl font-bold text-gray-900 mb-4"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            প্রধান ঔষধি গাছের চাষাবাদ
          </h2>
          <p
            className="text-lg text-gray-600"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            বর্তমানে প্রধান প্রধান ঔষধী গাছ গাছড়া চাষের জমির পরিমাণ
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Table */}
          <div className="lg:col-span-2">
            <Card className="backdrop-blur-sm bg-white/80 border-[#10c255]/10">
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-[#10c255]/10">
                      <tr>
                        <th
                          className="px-6 py-4 text-left text-sm font-semibold text-gray-900"
                          style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                        >
                          ক্রমিক নং
                        </th>
                        <th
                          className="px-6 py-4 text-left text-sm font-semibold text-gray-900"
                          style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                        >
                          ঔষধী গাছের নাম
                        </th>
                        <th
                          className="px-6 py-4 text-left text-sm font-semibold text-gray-900"
                          style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                        >
                          মোট চাষকৃত জমির পরিমাণ
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {herbalPlants.map((plant, index) => (
                        <tr key={index} className="hover:bg-[#10c255]/5 transition-colors">
                          <td
                            className="px-6 py-4 text-sm text-gray-900"
                            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                          >
                            {index === herbalPlants.length - 1 ? "মোট =" : `${index + 1}।`}
                          </td>
                          <td
                            className="px-6 py-4 text-sm text-gray-900"
                            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                          >
                            {plant.name}
                          </td>
                          <td
                            className="px-6 py-4 text-sm font-medium text-[#10c255]"
                            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                          >
                            {index === herbalPlants.length - 1 ? `${totalArea} বিঘা` : plant.area}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Side Stats and Info */}
          <div className="space-y-6">
            {/* Total Stats */}
            <Card className="backdrop-blur-sm bg-white/80 border-[#10c255]/10">
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-10 h-10 rounded-full bg-[#10c255]/10 flex items-center justify-center">
                    <BarChart3 className="h-5 w-5 text-[#10c255]" />
                  </div>
                  <h3
                    className="text-lg font-semibold"
                    style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                  >
                    মোট পরিসংখ্যান
                  </h3>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span
                      className="text-gray-600"
                      style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                    >
                      মোট জমি:
                    </span>
                    <span
                      className="font-bold text-[#10c255]"
                      style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                    >
                      ৯৬৭ বিঘা
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span
                      className="text-gray-600"
                      style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                    >
                      প্রজাতি:
                    </span>
                    <span
                      className="font-bold text-[#10c255]"
                      style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                    >
                      ৩০০+ প্রকার
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span
                      className="text-gray-600"
                      style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                    >
                      বৃহত্তম চাষ:
                    </span>
                    <span
                      className="font-bold text-[#10c255]"
                      style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                    >
                      শিমুল মূল
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Other Plants */}
            <Card className="backdrop-blur-sm bg-white/80 border-[#10c255]/10">
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-10 h-10 rounded-full bg-[#10c255]/10 flex items-center justify-center">
                    <Leaf className="h-5 w-5 text-[#10c255]" />
                  </div>
                  <h3
                    className="text-lg font-semibold"
                    style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                  >
                    অন্যান্য উল্লেখযোগ্য গাছ
                  </h3>
                </div>
                <div className="flex flex-wrap gap-2">
                  {otherPlants.map((plant, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="bg-[#10c255]/10 text-[#10c255] border-[#10c255]/20 hover:bg-[#10c255]/20 transition-colors"
                      style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                    >
                      {plant}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}
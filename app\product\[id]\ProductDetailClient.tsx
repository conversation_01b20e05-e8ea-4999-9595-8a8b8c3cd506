"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { useCart } from "@/contexts/CartContext"
import type { Product, ProductReview } from "@/lib/types"
import { formatFirestoreDate } from "@/lib/utils"
import {
  ShoppingCart,
  Heart,
  Share2,
  Star,
  Plus,
  Minus,
  Check,
  Truck,
  Shield,
  RotateCcw,
  ChevronLeft,
  ChevronRight,
  ThumbsUp,
} from "lucide-react"
import ProductCard from "@/app/components/ProductCard"

interface ProductDetailClientProps {
  product: Product
  reviews: ProductReview[]
  relatedProducts: Product[]
}

export default function ProductDetailClient({ product, reviews, relatedProducts }: ProductDetailClientProps) {
  const { addToCart, isInCart, getCartItem, updateQuantity } = useCart()
  const [selectedImage, setSelectedImage] = useState(0)
  const [quantity, setQuantity] = useState(1)
  const [activeTab, setActiveTab] = useState<"description" | "specifications" | "reviews">("description")
  const [isWishlisted, setIsWishlisted] = useState(false)
  const [isAdding, setIsAdding] = useState(false)
  const [showSuccess, setShowSuccess] = useState(false)

  const cartItem = getCartItem(product.id)
  const inCart = isInCart(product.id)
  const images = product.images || [product.image]

  const handleAddToCart = async () => {
    setIsAdding(true)
    addToCart(product, quantity)

    setShowSuccess(true)
    setTimeout(() => {
      setShowSuccess(false)
      setIsAdding(false)
    }, 1500)
  }

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity <= 0) return
    if (inCart && cartItem) {
      updateQuantity(product.id, newQuantity)
    } else {
      setQuantity(newQuantity)
    }
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating)
            ? "text-yellow-400 fill-current"
            : i < rating
              ? "text-yellow-400 fill-current opacity-50"
              : "text-gray-300"
        }`}
      />
    ))
  }

  const nextImage = () => {
    setSelectedImage((prev) => (prev + 1) % images.length)
  }

  const prevImage = () => {
    setSelectedImage((prev) => (prev - 1 + images.length) % images.length)
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Breadcrumb */}
      <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-8">
        <Link href="/" className="hover:text-blue-600">
          Home
        </Link>
        <span>/</span>
        <Link href="/shop" className="hover:text-blue-600">
          Shop
        </Link>
        <span>/</span>
        {product.category && (
          <>
            <span className="hover:text-blue-600">{product.category}</span>
            <span>/</span>
          </>
        )}
        <span className="text-gray-900">{product.name}</span>
      </nav>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
        {/* Product Images */}
        <div className="space-y-4">
          {/* Main Image */}
          <div className="relative aspect-square bg-gray-100 rounded-lg overflow-hidden">
            <Image
              src={images[selectedImage] || "/placeholder.svg?height=600&width=600&query=product"}
              alt={product.name}
              fill
              className="object-cover"
              priority
            />

            {/* Image Counter */}
            {images.length > 1 && (
              <div className="absolute top-4 right-4 bg-black/60 text-white px-3 py-1 rounded-full text-sm">
                {selectedImage + 1} / {images.length}
              </div>
            )}

            {images.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-800 p-2 rounded-full shadow-md transition-colors"
                >
                  <ChevronLeft className="h-5 w-5" />
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-800 p-2 rounded-full shadow-md transition-colors"
                >
                  <ChevronRight className="h-5 w-5" />
                </button>
              </>
            )}
          </div>

          {/* Thumbnail Images */}
          {images.length > 1 && (
            <div className="grid grid-cols-4 gap-4">
              {images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`relative aspect-square bg-gray-100 rounded-lg overflow-hidden border-2 transition-colors ${
                    selectedImage === index ? "border-blue-600" : "border-transparent hover:border-gray-300"
                  }`}
                >
                  <Image
                    src={image || "/placeholder.svg?height=150&width=150&query=product"}
                    alt={`${product.name} ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{product.name}</h1>
            {product.brand && <p className="text-lg text-gray-600 mb-4">by {product.brand}</p>}

            {/* Rating */}
            {product.rating && (
              <div className="flex items-center space-x-2 mb-4">
                <div className="flex items-center">{renderStars(product.rating)}</div>
                <span className="text-sm text-gray-600">
                  {product.rating} ({product.reviewCount} reviews)
                </span>
              </div>
            )}
          </div>

          {/* Price */}
          <div className="border-t border-b border-gray-200 py-6">
            <div className="flex items-center space-x-4 mb-2">
              {product.discountPrice && product.discountPrice > 0 ? (
                <div className="flex items-center space-x-3">
                  <span className="text-4xl font-bold text-red-600">${product.discountPrice.toFixed(2)}</span>
                  <span className="text-2xl text-gray-500 line-through">${product.price.toFixed(2)}</span>
                  <span className="bg-red-100 text-red-800 text-sm font-medium px-2 py-1 rounded">
                    {Math.round(((product.price - product.discountPrice) / product.price) * 100)}% OFF
                  </span>
                </div>
              ) : (
                <span className="text-4xl font-bold text-blue-600">${product.price.toFixed(2)}</span>
              )}
            </div>

            <div className="flex items-center space-x-4">
              {product.inStock ? (
                <span className="text-green-600 font-medium flex items-center">
                  <Check className="h-4 w-4 mr-1" />
                  In Stock
                </span>
              ) : (
                <span className="text-red-600 font-medium">Out of Stock</span>
              )}

              {product.stockQuantity !== undefined && (
                <span className="text-gray-600 text-sm">
                  {product.stockQuantity} units available
                </span>
              )}
            </div>

            {product.stockQuantity && product.stockQuantity < 10 && product.stockQuantity > 0 && (
              <p className="text-orange-600 text-sm mt-2 flex items-center">
                <span className="inline-block w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
                Only {product.stockQuantity} left in stock - order soon!
              </p>
            )}
          </div>

          {/* Product Information */}
          <div className="bg-gray-50 rounded-lg p-4 space-y-2">
            <h3 className="font-semibold text-gray-900 mb-3">Product Information</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
              {product.category && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Category:</span>
                  <span className="font-medium text-gray-900">{product.category}</span>
                </div>
              )}
              {product.brand && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Brand:</span>
                  <span className="font-medium text-gray-900">{product.brand}</span>
                </div>
              )}
              {product.stockQuantity !== undefined && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Stock Quantity:</span>
                  <span className="font-medium text-gray-900">{product.stockQuantity} units</span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-gray-600">Availability:</span>
                <span className={`font-medium ${product.inStock ? 'text-green-600' : 'text-red-600'}`}>
                  {product.inStock ? 'In Stock' : 'Out of Stock'}
                </span>
              </div>
              {product.createdAt && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Added:</span>
                  <span className="font-medium text-gray-900">
                    {formatFirestoreDate(product.createdAt)}
                  </span>
                </div>
              )}
              {product.updatedAt && product.updatedAt !== product.createdAt && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Last Updated:</span>
                  <span className="font-medium text-gray-900">
                    {formatFirestoreDate(product.updatedAt)}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Description */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Description</h3>
            <p className="text-gray-700 leading-relaxed">{product.description}</p>
          </div>

          {/* Key Features */}
          {product.features && product.features.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Key Features</h3>
              <ul className="space-y-2">
                {product.features.slice(0, 4).map((feature, index) => (
                  <li key={index} className="flex items-center text-gray-700">
                    <Check className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Quantity and Add to Cart */}
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium text-gray-700">Quantity:</span>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleQuantityChange((inCart && cartItem ? cartItem.quantity : quantity) - 1)}
                  className="w-10 h-10 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center transition-colors"
                >
                  <Minus className="h-4 w-4" />
                </button>
                <span className="font-medium text-lg min-w-[3rem] text-center">
                  {inCart && cartItem ? cartItem.quantity : quantity}
                </span>
                <button
                  onClick={() => handleQuantityChange((inCart && cartItem ? cartItem.quantity : quantity) + 1)}
                  className="w-10 h-10 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center transition-colors"
                >
                  <Plus className="h-4 w-4" />
                </button>
              </div>
            </div>

            <div className="flex space-x-4">
              <button
                onClick={handleAddToCart}
                disabled={isAdding || !product.inStock}
                className={`flex-1 flex items-center justify-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                  showSuccess
                    ? "bg-green-600 text-white"
                    : isAdding
                      ? "bg-blue-400 text-white cursor-not-allowed"
                      : !product.inStock
                        ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                        : "bg-blue-600 hover:bg-blue-700 text-white"
                }`}
              >
                {showSuccess ? (
                  <>
                    <Check className="h-5 w-5" />
                    <span>Added to Cart!</span>
                  </>
                ) : isAdding ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    <span>Adding...</span>
                  </>
                ) : (
                  <>
                    <ShoppingCart className="h-5 w-5" />
                    <span>{inCart ? "Update Cart" : "Add to Cart"}</span>
                  </>
                )}
              </button>

              <button
                onClick={() => setIsWishlisted(!isWishlisted)}
                className={`p-3 rounded-lg border-2 transition-colors ${
                  isWishlisted
                    ? "border-red-600 bg-red-50 text-red-600"
                    : "border-gray-300 hover:border-red-600 hover:text-red-600"
                }`}
              >
                <Heart className={`h-5 w-5 ${isWishlisted ? "fill-current" : ""}`} />
              </button>

              <button className="p-3 rounded-lg border-2 border-gray-300 hover:border-blue-600 hover:text-blue-600 transition-colors">
                <Share2 className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Shipping Info */}
          <div className="bg-gray-50 rounded-lg p-4 space-y-3">
            <div className="flex items-center text-sm text-gray-700">
              <Truck className="h-4 w-4 mr-2 text-green-600" />
              Free shipping on orders over $50
            </div>
            <div className="flex items-center text-sm text-gray-700">
              <Shield className="h-4 w-4 mr-2 text-blue-600" />
              2-year warranty included
            </div>
            <div className="flex items-center text-sm text-gray-700">
              <RotateCcw className="h-4 w-4 mr-2 text-purple-600" />
              30-day return policy
            </div>
          </div>
        </div>
      </div>

      {/* Product Details Tabs */}
      <div className="mb-16">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: "description", label: "Description" },
              { id: "specifications", label: "Specifications" },
              { id: "reviews", label: `Reviews (${reviews.length})` },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="py-8">
          {activeTab === "description" && (
            <div className="prose max-w-none">
              <p className="text-gray-700 leading-relaxed mb-6">{product.description}</p>

              {product.features && product.features.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Features</h3>
                  <ul className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {product.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-gray-700">
                        <Check className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          {activeTab === "specifications" && (
            <div className="space-y-8">
              {/* Product Specifications */}
              {product.specifications && Object.keys(product.specifications).length > 0 ? (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Technical Specifications</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {Object.entries(product.specifications).map(([key, value]) => (
                      <div key={key} className="flex justify-between py-3 border-b border-gray-200">
                        <span className="font-medium text-gray-900">{key}</span>
                        <span className="text-gray-700">{value}</span>
                      </div>
                    ))}
                  </div>
                </div>
              ) : null}

              {/* General Information */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">General Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="flex justify-between py-3 border-b border-gray-200">
                    <span className="font-medium text-gray-900">Product ID</span>
                    <span className="text-gray-700 font-mono text-sm">{product.id}</span>
                  </div>
                  {product.category && (
                    <div className="flex justify-between py-3 border-b border-gray-200">
                      <span className="font-medium text-gray-900">Category</span>
                      <span className="text-gray-700">{product.category}</span>
                    </div>
                  )}
                  {product.brand && (
                    <div className="flex justify-between py-3 border-b border-gray-200">
                      <span className="font-medium text-gray-900">Brand</span>
                      <span className="text-gray-700">{product.brand}</span>
                    </div>
                  )}
                  <div className="flex justify-between py-3 border-b border-gray-200">
                    <span className="font-medium text-gray-900">Regular Price</span>
                    <span className="text-gray-700">${product.price.toFixed(2)}</span>
                  </div>
                  {product.discountPrice && product.discountPrice > 0 && (
                    <div className="flex justify-between py-3 border-b border-gray-200">
                      <span className="font-medium text-gray-900">Discount Price</span>
                      <span className="text-gray-700">${product.discountPrice.toFixed(2)}</span>
                    </div>
                  )}
                  <div className="flex justify-between py-3 border-b border-gray-200">
                    <span className="font-medium text-gray-900">Stock Status</span>
                    <span className={`${product.inStock ? 'text-green-600' : 'text-red-600'}`}>
                      {product.inStock ? 'In Stock' : 'Out of Stock'}
                    </span>
                  </div>
                  {product.stockQuantity !== undefined && (
                    <div className="flex justify-between py-3 border-b border-gray-200">
                      <span className="font-medium text-gray-900">Available Quantity</span>
                      <span className="text-gray-700">{product.stockQuantity} units</span>
                    </div>
                  )}
                  {product.rating && (
                    <div className="flex justify-between py-3 border-b border-gray-200">
                      <span className="font-medium text-gray-900">Average Rating</span>
                      <span className="text-gray-700">{product.rating}/5 ({product.reviewCount} reviews)</span>
                    </div>
                  )}
                  {product.images && product.images.length > 0 && (
                    <div className="flex justify-between py-3 border-b border-gray-200">
                      <span className="font-medium text-gray-900">Total Images</span>
                      <span className="text-gray-700">{product.images.length + 1} images</span>
                    </div>
                  )}
                  {product.createdAt && (
                    <div className="flex justify-between py-3 border-b border-gray-200">
                      <span className="font-medium text-gray-900">Date Added</span>
                      <span className="text-gray-700">{formatFirestoreDate(product.createdAt)}</span>
                    </div>
                  )}
                  {product.updatedAt && product.updatedAt !== product.createdAt && (
                    <div className="flex justify-between py-3 border-b border-gray-200">
                      <span className="font-medium text-gray-900">Last Updated</span>
                      <span className="text-gray-700">{formatFirestoreDate(product.updatedAt)}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* No specifications message */}
              {(!product.specifications || Object.keys(product.specifications).length === 0) && (
                <div className="text-center py-8">
                  <p className="text-gray-500">No technical specifications available for this product.</p>
                </div>
              )}
            </div>
          )}

          {activeTab === "reviews" && (
            <div className="space-y-8">
              {reviews.length > 0 ? (
                <>
                  {/* Reviews Summary */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <div className="flex items-center space-x-6">
                      <div className="text-center">
                        <div className="text-4xl font-bold text-gray-900">{product.rating}</div>
                        <div className="flex items-center justify-center mt-1">{renderStars(product.rating || 0)}</div>
                        <div className="text-sm text-gray-600 mt-1">Based on {product.reviewCount} reviews</div>
                      </div>
                    </div>
                  </div>

                  {/* Individual Reviews */}
                  <div className="space-y-6">
                    {reviews.map((review) => (
                      <div key={review.id} className="border-b border-gray-200 pb-6">
                        <div className="flex items-start space-x-4">
                          <div className="flex-shrink-0">
                            <Image
                              src={review.userPhoto || "/placeholder.svg?height=40&width=40&query=user-avatar"}
                              alt={review.userName}
                              width={40}
                              height={40}
                              className="rounded-full"
                            />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <span className="font-medium text-gray-900">{review.userName}</span>
                              <div className="flex items-center">{renderStars(review.rating)}</div>
                              <span className="text-sm text-gray-500">
                                {new Date(review.createdAt).toLocaleDateString()}
                              </span>
                            </div>
                            <h4 className="font-medium text-gray-900 mb-2">{review.title}</h4>
                            <p className="text-gray-700 mb-3">{review.comment}</p>
                            <button className="flex items-center text-sm text-gray-500 hover:text-gray-700">
                              <ThumbsUp className="h-4 w-4 mr-1" />
                              Helpful ({review.helpful})
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">No reviews yet. Be the first to review this product!</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Related Products */}
      {relatedProducts.length > 0 && (
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-8">Related Products</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {relatedProducts.map((relatedProduct) => (
              <ProductCard key={relatedProduct.id} product={relatedProduct} />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

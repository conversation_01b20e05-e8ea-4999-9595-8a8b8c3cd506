import { FieldValue } from "firebase/firestore"

export interface Product {
  id: string
  name: string
  image: string
  price: number
  discountPrice?: number
  description: string
  category?: string
  brand?: string
  inStock?: boolean
  stockQuantity?: number
  images?: string[]
  specifications?: { [key: string]: string }
  features?: string[]
  rating?: number
  reviewCount?: number
  createdAt?: string | FieldValue
  updatedAt?: string | FieldValue
}

export interface CartItem extends Product {
  quantity: number
}

export interface ProductReview {
  id: string
  productId: string
  userId: string
  userName: string
  userPhoto?: string
  rating: number
  title: string
  comment: string
  createdAt: string | Date
  helpful: number
}

// Order-related interfaces
export interface OrderItem {
  id: string
  name: string
  image: string
  price: number
  quantity: number
  total: number
}

export interface ShippingInfo {
  name: string
  phone: string
  address: string
  notes?: string
}

export interface Order {
  id: string
  orderNumber: string
  items: OrderItem[]
  shippingInfo: ShippingInfo
  subtotal: number
  shipping: number
  tax: number
  total: number
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded'
  isGuestOrder: boolean
  userId?: string // Only present for authenticated users
  userEmail?: string // For both guest and authenticated users
  createdAt: string | Date
  updatedAt: string | Date
  notes?: string
}

export interface GuestCustomer {
  name: string
  phone: string
  email?: string
  address: string
}

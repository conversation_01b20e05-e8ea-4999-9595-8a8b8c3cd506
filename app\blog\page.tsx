import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Calendar, Clock, User, ArrowRight, Leaf, Heart, Star } from 'lucide-react'

export const metadata: Metadata = {
  title: 'ভেষজ স্বাস্থ্য ব্লগ | Oshudhigram - প্রাকৃতিক চিকিৎসা গাইড',
  description: 'ভেষজ ওষুধ, প্রাকৃতিক চিকিৎসা, এলোভেরা, তুলসী, অশ্বগন্ধার উপকারিতা ও ব্যবহার সম্পর্কে বিস্তারিত জানুন। বিশেষজ্ঞদের পরামর্শ ও গবেষণা।',
  keywords: [
    'ভেষজ ওষুধ ব্লগ',
    'প্রাকৃতিক চিকিৎসা গাইড',
    'এলোভেরার উপকারিতা',
    'তুলসীর গুণাগুণ',
    'অশ্বগন্ধার ব্যবহার',
    'হার্বাল মেডিসিন বাংলা',
    'আয়ুর্বেদিক চিকিৎসা',
    'ভেষজ গাছের উপকারিতা',
    'প্রাকৃতিক স্বাস্থ্য টিপস',
    'herbal medicine blog bangladesh'
  ].join(', '),
  openGraph: {
    title: 'ভেষজ স্বাস্থ্য ব্লগ | Oshudhigram',
    description: 'ভেষজ ওষুধ ও প্রাকৃতিক চিকিৎসা সম্পর্কে বিশেষজ্ঞদের পরামর্শ',
    images: ['/blog-og-image.jpg'],
  },
}

const blogPosts = [
  {
    id: 'aloe-vera-benefits-bangla',
    title: 'এলোভেরার ১০টি অবিশ্বাস্য উপকারিতা যা আপনি জানেন না',
    excerpt: 'এলোভেরা শুধু ত্বকের যত্নেই নয়, হজম, রোগ প্রতিরোধ ক্ষমতা বৃদ্ধি এবং আরও অনেক স্বাস্থ্য সমস্যার সমাধানে কার্যকর।',
    image: '/blog/aloe-vera-benefits.jpg',
    category: 'এলোভেরা',
    author: 'ডা. রহিমা খাতুন',
    date: '২০২৫-০১-১০',
    readTime: '৫ মিনিট',
    tags: ['এলোভেরা', 'ত্বকের যত্ন', 'প্রাকৃতিক চিকিৎসা'],
    featured: true,
  },
  {
    id: 'tulsi-medicinal-properties',
    title: 'তুলসী: প্রকৃতির সবচেয়ে শক্তিশালী ওষুধ',
    excerpt: 'তুলসীর অ্যান্টিভাইরাল, অ্যান্টিব্যাকটেরিয়াল এবং অ্যান্টিঅক্সিডেন্ট গুণাগুণ কীভাবে আপনার স্বাস্থ্য রক্ষা করে।',
    image: '/blog/tulsi-benefits.jpg',
    category: 'তুলসী',
    author: 'হাকিম আব্দুল করিম',
    date: '২০২৫-০১-০৮',
    readTime: '৭ মিনিট',
    tags: ['তুলসী', 'রোগ প্রতিরোধ', 'শ্বাসকষ্ট'],
    featured: true,
  },
  {
    id: 'ashwagandha-uses-bangladesh',
    title: 'অশ্বগন্ধা: স্ট্রেস ও দুর্বলতার প্রাকৃতিক সমাধান',
    excerpt: 'অশ্বগন্ধা কীভাবে মানসিক চাপ কমায়, শক্তি বৃদ্ধি করে এবং হরমোনের ভারসাম্য রক্ষা করে।',
    image: '/blog/ashwagandha-benefits.jpg',
    category: 'অশ্বগন্ধা',
    author: 'ডা. ফারুক আহমেদ',
    date: '২০২৫-০১-০৫',
    readTime: '৬ মিনিট',
    tags: ['অশ্বগন্ধা', 'স্ট্রেস', 'শক্তি বৃদ্ধি'],
    featured: false,
  },
  {
    id: 'herbal-medicine-guide',
    title: 'ভেষজ ওষুধ ব্যবহারের সঠিক নিয়ম ও সতর্কতা',
    excerpt: 'ভেষজ ওষুধ নিরাপদে ব্যবহারের জন্য জানুন সঠিক মাত্রা, সময় এবং পার্শ্বপ্রতিক্রিয়া সম্পর্কে।',
    image: '/blog/herbal-guide.jpg',
    category: 'গাইড',
    author: 'প্রফেসর ড. নাসির উদ্দিন',
    date: '২০২৫-০১-০৩',
    readTime: '১০ মিনিট',
    tags: ['ভেষজ ওষুধ', 'নিরাপত্তা', 'ডোজ'],
    featured: false,
  },
  {
    id: 'natore-herbal-village-history',
    title: 'নাটোর ঔষধি গ্রামের ইতিহাস ও ঐতিহ্য',
    excerpt: 'কীভাবে নাটোর জেলায় গড়ে উঠেছে বাংলাদেশের সবচেয়ে বড় ভেষজ গ্রাম এবং এর সাংস্কৃতিক গুরুত্ব।',
    image: '/blog/natore-village.jpg',
    category: 'ইতিহাস',
    author: 'গবেষক মোহাম্মদ আলী',
    date: '২০২৫-০১-০১',
    readTime: '৮ মিনিট',
    tags: ['নাটোর', 'ইতিহাস', 'ঐতিহ্য'],
    featured: false,
  },
  {
    id: 'seasonal-herbal-remedies',
    title: 'ঋতু অনুযায়ী ভেষজ চিকিৎসা: বছরব্যাপী সুস্থ থাকুন',
    excerpt: 'বিভিন্ন ঋতুতে কোন ভেষজ ওষুধ ব্যবহার করবেন এবং কীভাবে প্রাকৃতিক উপায়ে রোগ প্রতিরোধ করবেন।',
    image: '/blog/seasonal-herbs.jpg',
    category: 'ঋতুভিত্তিক',
    author: 'ডা. সালমা আক্তার',
    date: '২০২৪-১২-২৮',
    readTime: '৯ মিনিট',
    tags: ['ঋতু', 'প্রতিরোধ', 'প্রাকৃতিক'],
    featured: false,
  },
]

const categories = [
  { name: 'এলোভেরা', count: 12, color: 'bg-green-100 text-green-800' },
  { name: 'তুলসী', count: 8, color: 'bg-blue-100 text-blue-800' },
  { name: 'অশ্বগন্ধা', count: 6, color: 'bg-purple-100 text-purple-800' },
  { name: 'গাইড', count: 15, color: 'bg-orange-100 text-orange-800' },
  { name: 'গবেষণা', count: 10, color: 'bg-red-100 text-red-800' },
  { name: 'ইতিহাস', count: 4, color: 'bg-yellow-100 text-yellow-800' },
]

export default function BlogPage() {
  const featuredPosts = blogPosts.filter(post => post.featured)
  const regularPosts = blogPosts.filter(post => !post.featured)

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-50 pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <Badge className="mb-4 bg-[#10c255]/10 text-[#10c255] border-[#10c255]/20">
            <Leaf className="h-4 w-4 mr-2" />
            ভেষজ স্বাস্থ্য ব্লগ
          </Badge>
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
            প্রাকৃতিক চিকিৎসার জ্ঞানভাণ্ডার
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
            ভেষজ ওষুধ, প্রাকৃতিক চিকিৎসা এবং স্বাস্থ্যকর জীবনযাত্রা সম্পর্কে বিশেষজ্ঞদের পরামর্শ ও গবেষণা
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Featured Posts */}
            {featuredPosts.length > 0 && (
              <section className="mb-12">
                <h2 className="text-2xl font-bold mb-6 flex items-center" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                  <Star className="h-6 w-6 mr-2 text-yellow-500" />
                  বিশেষ নিবন্ধ
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {featuredPosts.map((post) => (
                    <Card key={post.id} className="group hover:shadow-xl transition-all duration-300 overflow-hidden">
                      <div className="relative h-48">
                        <Image
                          src={post.image}
                          alt={post.title}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                        <Badge className={`absolute top-4 left-4 ${post.category === 'এলোভেরা' ? 'bg-green-100 text-green-800' : 
                          post.category === 'তুলসী' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'}`}>
                          {post.category}
                        </Badge>
                      </div>
                      <CardContent className="p-6">
                        <h3 className="text-xl font-semibold mb-3 group-hover:text-[#10c255] transition-colors" 
                            style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                          <Link href={`/blog/${post.id}`}>
                            {post.title}
                          </Link>
                        </h3>
                        <p className="text-gray-600 mb-4 line-clamp-3" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                          {post.excerpt}
                        </p>
                        <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                          <div className="flex items-center gap-4">
                            <span className="flex items-center gap-1">
                              <User className="h-4 w-4" />
                              {post.author}
                            </span>
                            <span className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              {post.date}
                            </span>
                            <span className="flex items-center gap-1">
                              <Clock className="h-4 w-4" />
                              {post.readTime}
                            </span>
                          </div>
                        </div>
                        <Button variant="outline" className="w-full group-hover:bg-[#10c255] group-hover:text-white transition-colors" asChild>
                          <Link href={`/blog/${post.id}`} style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                            পড়ুন
                            <ArrowRight className="h-4 w-4 ml-2" />
                          </Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>
            )}

            {/* Regular Posts */}
            <section>
              <h2 className="text-2xl font-bold mb-6" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                সকল নিবন্ধ
              </h2>
              <div className="space-y-6">
                {regularPosts.map((post) => (
                  <Card key={post.id} className="group hover:shadow-lg transition-all duration-300">
                    <CardContent className="p-0">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-0">
                        <div className="relative h-48 md:h-auto">
                          <Image
                            src={post.image}
                            alt={post.title}
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                        </div>
                        <div className="md:col-span-2 p-6">
                          <div className="flex items-center gap-2 mb-3">
                            <Badge className={categories.find(cat => cat.name === post.category)?.color || 'bg-gray-100 text-gray-800'}>
                              {post.category}
                            </Badge>
                            <div className="flex gap-1">
                              {post.tags.slice(0, 2).map((tag) => (
                                <Badge key={tag} variant="outline" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          <h3 className="text-xl font-semibold mb-3 group-hover:text-[#10c255] transition-colors" 
                              style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                            <Link href={`/blog/${post.id}`}>
                              {post.title}
                            </Link>
                          </h3>
                          <p className="text-gray-600 mb-4" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                            {post.excerpt}
                          </p>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4 text-sm text-gray-500">
                              <span className="flex items-center gap-1">
                                <User className="h-4 w-4" />
                                {post.author}
                              </span>
                              <span className="flex items-center gap-1">
                                <Calendar className="h-4 w-4" />
                                {post.date}
                              </span>
                              <span className="flex items-center gap-1">
                                <Clock className="h-4 w-4" />
                                {post.readTime}
                              </span>
                            </div>
                            <Button variant="ghost" size="sm" className="group-hover:bg-[#10c255] group-hover:text-white transition-colors" asChild>
                              <Link href={`/blog/${post.id}`} style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                                পড়ুন
                                <ArrowRight className="h-4 w-4 ml-1" />
                              </Link>
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </section>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-24 space-y-6">
              {/* Categories */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                    <Heart className="h-5 w-5 mr-2 text-[#10c255]" />
                    বিষয়সমূহ
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {categories.map((category) => (
                      <Link
                        key={category.name}
                        href={`/blog?category=${category.name}`}
                        className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        <span style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                          {category.name}
                        </span>
                        <Badge variant="secondary" className="text-xs">
                          {category.count}
                        </Badge>
                      </Link>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Newsletter */}
              <Card className="bg-gradient-to-br from-[#10c255]/10 to-[#10c255]/5">
                <CardContent className="p-6 text-center">
                  <Leaf className="h-12 w-12 mx-auto mb-4 text-[#10c255]" />
                  <h3 className="text-lg font-semibold mb-2" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                    স্বাস্থ্য টিপস পান
                  </h3>
                  <p className="text-sm text-gray-600 mb-4" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                    নিয়মিত ভেষজ স্বাস্থ্য টিপস ও নতুন নিবন্ধের আপডেট পেতে সাবস্ক্রাইব করুন
                  </p>
                  <Button className="w-full bg-[#10c255] hover:bg-[#0ea048]" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                    সাবস্ক্রাইব করুন
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export const dynamic = 'force-static';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Heart, Shield, Leaf, Zap, Brain, Sparkles } from "lucide-react";

export default function TraditionalMedicine() {
  const benefits = [
    {
      icon: <Heart className="h-6 w-6" />,
      title: "হৃদরোগের চিকিৎসা",
      description: "অর্জুন, গার্জন ও পুনর্নবা দিয়ে প্রাকৃতিক হৃদরোগের চিকিৎসা",
      color: "bg-red-100 text-red-600",
      stats: "৮৫% কার্যকর"
    },
    {
      icon: <Brain className="h-6 w-6" />,
      title: "স্নায়ুতন্ত্রের সুস্থতা",
      description: "ব্রাহ্মী, শঙ্খপুষ্পী ও মানসিক স্বাস্থ্যের উন্নতিতে কার্যকর",
      color: "bg-purple-100 text-purple-600",
      stats: "৯০% উন্নতি"
    },
    {
      icon: <Shield className="h-6 w-6" />,
      title: "রোগ প্রতিরোধ ক্ষমতা",
      description: "তুল<PERSON>ী, গিলয় ও অশ্বগন্ধা দিয়ে প্রাকৃতিক ইমিউনিটি বৃদ্ধি",
      color: "bg-green-100 text-green-600",
      stats: "৯৫% বৃদ্ধি"
    },
    {
      icon: <Zap className="h-6 w-6" />,
      title: "শক্তি ও স্ট্যামিনা",
      description: "শতাবরী, সফেদ মুসলি ও প্রাকৃতিক শক্তি বর্ধক",
      color: "bg-yellow-100 text-yellow-600",
      stats: "৮০% বৃদ্ধি"
    },
    {
      icon: <Leaf className="h-6 w-6" />,
      title: "পাচনতন্ত্রের সুস্থতা",
      description: "ত্রিফলা, আমলকী ও পেটের সমস্যার প্রাকৃতিক সমাধান",
      color: "bg-blue-100 text-blue-600",
      stats: "৯২% সুস্থতা"
    },
    {
      icon: <Sparkles className="h-6 w-6" />,
      title: "ত্বক ও চুলের যত্ন",
      description: "নিম, অ্যালোভেরা ও প্রাকৃতিক সৌন্দর্য বর্ধক",
      color: "bg-pink-100 text-pink-600",
      stats: "৮৮% উন্নতি"
    }
  ];

  const researchData = [
    {
      title: "আয়ুর্বেদিক নীতি",
      description: "৫০০০ বছরের পুরানো আয়ুর্বেদিক চিকিৎসা পদ্ধতি অনুসরণ",
      value: "৫০০০+",
      unit: "বছর"
    },
    {
      title: "ক্লিনিক্যাল ট্রায়াল",
      description: "আন্তর্জাতিক মানের ক্লিনিক্যাল ট্রায়াল সম্পন্ন",
      value: "১২০+",
      unit: "ট্রায়াল"
    },
    {
      title: "সফলতার হার",
      description: "ভেষজ চিকিৎসায় রোগীদের সন্তুষ্টির হার",
      value: "৯৫%",
      unit: "সফলতা"
    },
    {
      title: "পার্শ্বপ্রতিক্রিয়া",
      description: "প্রাকৃতিক ভেষজ চিকিৎসায় পার্শ্বপ্রতিক্রিয়ার হার",
      value: "০.৫%",
      unit: "মাত্র"
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-green-50/30 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <Badge
            className="mb-4 bg-[#10c255]/10 text-[#10c255] border-[#10c255]/20"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            ঐতিহ্যবাহী চিকিৎসা
          </Badge>
          <h2
            className="text-4xl font-bold text-gray-900 mb-4"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            প্রাকৃতিক ভেষজ চিকিৎসার উপকারিতা
          </h2>
          <p
            className="text-lg text-gray-600 max-w-3xl mx-auto"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            হাজার বছরের ঐতিহ্যবাহী জ্ঞান ও আধুনিক বিজ্ঞানের সমন্বয়ে প্রাকৃতিক চিকিৎসা
          </p>
        </div>

        {/* Benefits Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
          {benefits.map((benefit, index) => (
            <Card
              key={index}
              className="group hover:shadow-xl transition-all duration-300 backdrop-blur-sm bg-white/80 border-[#10c255]/10 hover:border-[#10c255]/30"
            >
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className={`w-12 h-12 rounded-full ${benefit.color} flex items-center justify-center group-hover:scale-110 transition-transform`}>
                    {benefit.icon}
                  </div>
                  <div className="flex-1">
                    <h3
                      className="text-lg font-semibold text-gray-900"
                      style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                    >
                      {benefit.title}
                    </h3>
                    <div className="text-sm font-medium text-[#10c255]">
                      {benefit.stats}
                    </div>
                  </div>
                </div>
                <p
                  className="text-gray-600 text-sm leading-relaxed"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  {benefit.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Research Statistics */}
        <Card className="backdrop-blur-sm bg-white/80 border-[#10c255]/10 mb-12">
          <CardContent className="p-8">
            <div className="text-center mb-8">
              <h3
                className="text-2xl font-bold text-gray-900 mb-2"
                style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
              >
                গবেষণা ও পরিসংখ্যান
              </h3>
              <p
                className="text-gray-600"
                style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
              >
                বৈজ্ঞানিক গবেষণা ও ক্লিনিক্যাল ট্রায়ালের ফলাফল
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {researchData.map((data, index) => (
                <div key={index} className="text-center p-6 bg-[#10c255]/5 rounded-xl">
                  <div
                    className="text-3xl font-bold text-[#10c255] mb-1"
                    style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                  >
                    {data.value}
                  </div>
                  <div
                    className="text-sm text-gray-500 mb-2"
                    style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                  >
                    {data.unit}
                  </div>
                  <h4
                    className="font-semibold text-gray-900 mb-2"
                    style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                  >
                    {data.title}
                  </h4>
                  <p
                    className="text-xs text-gray-600"
                    style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                  >
                    {data.description}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Call to Action */}
        <div className="text-center">
          <Card className="backdrop-blur-sm bg-gradient-to-r from-[#10c255]/10 to-green-100/50 border-[#10c255]/20">
            <CardContent className="p-8">
              <h3
                className="text-2xl font-bold text-gray-900 mb-4"
                style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
              >
                প্রাকৃতিক চিকিৎসার সুবিধা নিন
              </h3>
              <p
                className="text-gray-600 mb-6 max-w-2xl mx-auto"
                style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
              >
                আমাদের অভিজ্ঞ ভেষজ চিকিৎসকদের পরামর্শ নিয়ে প্রাকৃতিক উপায়ে সুস্থ থাকুন। 
                কোনো পার্শ্বপ্রতিক্রিয়া ছাড়াই দীর্ঘমেয়াদী সুস্থতা নিশ্চিত করুন।
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Badge className="bg-[#10c255] text-white px-6 py-2 text-sm">
                  <span style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}>
                    ২৪/৭ পরামর্শ সেবা
                  </span>
                </Badge>
                <Badge className="bg-blue-500 text-white px-6 py-2 text-sm">
                  <span style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}>
                    বিনামূল্যে প্রাথমিক পরামর্শ
                  </span>
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

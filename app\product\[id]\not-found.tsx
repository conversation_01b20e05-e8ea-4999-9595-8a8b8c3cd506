import Link from "next/link"
import { Package, ArrowLeft } from "lucide-react"

export default function NotFound() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div className="text-center">
        <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h1
          className="text-3xl font-bold text-gray-900 mb-4"
          style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
        >
          পণ্য পাওয়া যায়নি
        </h1>
        <p
          className="text-lg text-gray-600 mb-8"
          style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
        >
          দুঃখিত, আপনি যে পণ্যটি খুঁজছেন তা আমরা খুঁজে পাইনি। এটি সরানো হয়ে থাকতে পারে বা অস্তিত্ব নেই।
        </p>
        <div className="space-y-4">
          <Link
            href="/shop"
            className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            দোকানে ফিরে যান
          </Link>
          <div>
            <Link
              href="/"
              className="text-blue-600 hover:text-blue-800"
              style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
            >
              হোমপেজে যান
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

import { getProducts } from "@/lib/firestore"
import { isFirebaseConfigured } from "@/lib/firebase"
import ProductGrid from "../components/ProductGrid"
import type { Product } from "@/lib/types"

export default async function ShopPage() {
  let products: Product[] = []
  let error: string | null = null
  try {
    products = await getProducts()
  } catch (err) {
    error = "Failed to load products. Please try again later."
    console.error("Error in ShopPage:", err)
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

      <div className="mb-8">
        <h1
          className="text-3xl font-bold text-gray-900 mb-4"
          style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
        >
          আমাদের পণ্যসমূহ
        </h1>
        <p
          className="text-lg text-gray-600"
          style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
        >
          আমাদের সম্পূর্ণ ভেষজ পণ্যের সংগ্রহ আবিষ্কার করুন
        </p>
      </div>

      {error ? (
        <div className="text-center py-12">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
            <p
              className="text-red-600 mb-4"
              style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
            >
              পণ্য লোড করতে সমস্যা হয়েছে। অনুগ্রহ করে পরে আবার চেষ্টা করুন।
            </p>
            <p
              className="text-sm text-gray-600"
              style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
            >
              নিশ্চিত করুন যে আপনার Firebase কনফিগারেশন সঠিকভাবে সেট আপ করা হয়েছে এবং আপনার Firestore ডাটাবেসে পণ্য রয়েছে।
            </p>
          </div>
        </div>
      ) : (
        <ProductGrid products={products} />
      )}
    </div>
  )
}

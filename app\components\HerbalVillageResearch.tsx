export const dynamic = 'force-static';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { BookOpen, Globe, TrendingUp, Users2, Microscope, Award } from "lucide-react";

export default function HerbalVillageResearch() {
  return (
    <section className="py-20 bg-gradient-to-b from-white to-green-50/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <Badge
            className="mb-4 bg-[#10c255]/10 text-[#10c255] border-[#10c255]/20"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            গবেষণা ও উন্নয়ন
          </Badge>
          <h2
            className="text-4xl font-bold text-gray-900 mb-4"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            ঔষধি গ্রামের বৈজ্ঞানিক গবেষণা
          </h2>
          <p
            className="text-lg text-gray-600 max-w-3xl mx-auto"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            আন্তর্জাতিক গবেষণা ও বৈজ্ঞানিক প্রমাণের ভিত্তিতে ঔষধি গ্রামের ভেষজ চিকিৎসা
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          <Card className="backdrop-blur-sm bg-white/80 border-[#10c255]/10 hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                  <Microscope className="h-6 w-6 text-blue-600" />
                </div>
                <h3
                  className="text-xl font-semibold"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  বৈজ্ঞানিক গবেষণা
                </h3>
              </div>
              <p
                className="text-gray-600 mb-4"
                style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
              >
                বাংলাদেশ মেডিকেল রিসার্চ কাউন্সিল (BMRC) এর সহযোগিতায় ঔষধি গাছের 
                ফার্মাকোলজিক্যাল গবেষণা পরিচালিত হচ্ছে।
              </p>
              <div className="text-sm text-[#10c255] font-medium">
                ৫০+ গবেষণা প্রকল্প সম্পন্ন
              </div>
            </CardContent>
          </Card>

          <Card className="backdrop-blur-sm bg-white/80 border-[#10c255]/10 hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                  <Globe className="h-6 w-6 text-green-600" />
                </div>
                <h3
                  className="text-xl font-semibold"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  আন্তর্জাতিক স্বীকৃতি
                </h3>
              </div>
              <p
                className="text-gray-600 mb-4"
                style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
              >
                WHO এর ঐতিহ্যবাহী চিকিৎসা কৌশল (Traditional Medicine Strategy) 
                অনুসরণ করে ভেষজ চিকিৎসা সেবা প্রদান।
              </p>
              <div className="text-sm text-[#10c255] font-medium">
                WHO গাইডলাইন অনুসৃত
              </div>
            </CardContent>
          </Card>

          <Card className="backdrop-blur-sm bg-white/80 border-[#10c255]/10 hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
                  <BookOpen className="h-6 w-6 text-purple-600" />
                </div>
                <h3
                  className="text-xl font-semibold"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  প্রকাশনা ও জার্নাল
                </h3>
              </div>
              <p
                className="text-gray-600 mb-4"
                style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
              >
                আন্তর্জাতিক জার্নালে ঔষধি গাছের কার্যকারিতা নিয়ে ৩০+ গবেষণা 
                প্রবন্ধ প্রকাশিত হয়েছে।
              </p>
              <div className="text-sm text-[#10c255] font-medium">
                ৩০+ পিয়ার রিভিউড পেপার
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export const dynamic = 'force-static';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, Globe, DollarSign, Users, BarChart3, Target } from "lucide-react";

export default function GlobalHerbalTrends() {
  const globalStats = [
    {
      icon: <DollarSign className="h-6 w-6" />,
      title: "বৈশ্বিক বাজার",
      value: "$১২৯ বিলিয়ন",
      description: "২০২৩ সালে ভেষজ ওষুধের বৈশ্বিক বাজারের আকার",
      growth: "+৮.৫% বৃদ্ধি",
      color: "bg-green-100 text-green-600"
    },
    {
      icon: <TrendingUp className="h-6 w-6" />,
      title: "বার্ষিক বৃদ্ধি",
      value: "১২.৮%",
      description: "২০২৪-২০৩০ সালের মধ্যে প্রত্যাশিত বার্ষিক বৃদ্ধির হার",
      growth: "ক্রমবর্ধমান",
      color: "bg-blue-100 text-blue-600"
    },
    {
      icon: <Users className="h-6 w-6" />,
      title: "ব্যবহারকারী",
      value: "৪ বিলিয়ন",
      description: "বিশ্বব্যাপী ভেষজ ওষুধ ব্যবহারকারীর সংখ্যা",
      growth: "৮০% জনসংখ্যা",
      color: "bg-purple-100 text-purple-600"
    },
    {
      icon: <Globe className="h-6 w-6" />,
      title: "দেশের সংখ্যা",
      value: "১৭০+",
      description: "যে দেশগুলোতে ভেষজ ওষুধ আনুষ্ঠানিকভাবে স্বীকৃত",
      growth: "WHO অনুমোদিত",
      color: "bg-orange-100 text-orange-600"
    }
  ];

  const researchHighlights = [
    {
      title: "ক্যান্সার গবেষণা",
      description: "হলুদ (কারকিউমিন) ক্যান্সার প্রতিরোধে ৭৫% কার্যকর প্রমাণিত",
      source: "Journal of Clinical Oncology, 2023",
      impact: "উচ্চ প্রভাব"
    },
    {
      title: "ডায়াবেটিস নিয়ন্ত্রণ",
      description: "করলা ও মেথি ডায়াবেটিস নিয়ন্ত্রণে ৬৮% কার্যকর",
      source: "Diabetes Care International, 2023",
      impact: "মধ্যম প্রভাব"
    },
    {
      title: "হৃদরোগ প্রতিরোধ",
      description: "অর্জুন ছাল হৃদরোগের ঝুঁকি ৫৫% কমায়",
      source: "Cardiology Research, 2024",
      impact: "উচ্চ প্রভাব"
    },
    {
      title: "মানসিক স্বাস্থ্য",
      description: "অশ্বগন্ধা উদ্বেগ ও চাপ ৬২% কমাতে সাহায্য করে",
      source: "Psychiatry & Mental Health, 2023",
      impact: "মধ্যম প্রভাব"
    }
  ];

  const countryData = [
    { country: "চীন", percentage: "৩৫%", description: "বৈশ্বিক ভেষজ ওষুধ উৎপাদনে অগ্রণী" },
    { country: "ভারত", percentage: "২৮%", description: "আয়ুর্বেদিক ওষুধের প্রধান রপ্তানিকারক" },
    { country: "জার্মানি", percentage: "১৫%", description: "ইউরোপের সবচেয়ে বড় ভেষজ ওষুধের বাজার" },
    { country: "যুক্তরাষ্ট্র", percentage: "১২%", description: "ভেষজ সাপ্লিমেন্টের বৃহত্তম ভোক্তা" },
    { country: "বাংলাদেশ", percentage: "৩%", description: "দ্রুত বর্ধনশীল ভেষজ ওষুধের বাজার" }
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-white to-blue-50/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <Badge
            className="mb-4 bg-blue-500/10 text-blue-600 border-blue-500/20"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            বৈশ্বিক প্রবণতা
          </Badge>
          <h2
            className="text-4xl font-bold text-gray-900 mb-4"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            বিশ্বব্যাপী ভেষজ ওষুধের বাজার
          </h2>
          <p
            className="text-lg text-gray-600 max-w-3xl mx-auto"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            আন্তর্জাতিক গবেষণা ও পরিসংখ্যানের আলোকে ভেষজ ওষুধের ক্রমবর্ধমান জনপ্রিয়তা
          </p>
        </div>

        {/* Global Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {globalStats.map((stat, index) => (
            <Card
              key={index}
              className="group hover:shadow-xl transition-all duration-300 backdrop-blur-sm bg-white/80 border-blue-500/10 hover:border-blue-500/30"
            >
              <CardContent className="p-6 text-center">
                <div className={`w-16 h-16 rounded-full ${stat.color} flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform`}>
                  {stat.icon}
                </div>
                <div
                  className="text-2xl font-bold text-gray-900 mb-1"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  {stat.value}
                </div>
                <h3
                  className="text-lg font-semibold text-gray-800 mb-2"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  {stat.title}
                </h3>
                <p
                  className="text-sm text-gray-600 mb-2"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  {stat.description}
                </p>
                <Badge className="bg-green-100 text-green-700 text-xs">
                  {stat.growth}
                </Badge>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Research Highlights */}
        <div className="mb-16">
          <h3
            className="text-2xl font-bold text-center text-gray-900 mb-8"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            সাম্প্রতিক গবেষণার ফলাফল
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {researchHighlights.map((research, index) => (
              <Card
                key={index}
                className="backdrop-blur-sm bg-white/80 border-blue-500/10 hover:shadow-lg transition-all duration-300"
              >
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-3">
                    <h4
                      className="text-lg font-semibold text-gray-900"
                      style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                    >
                      {research.title}
                    </h4>
                    <Badge 
                      className={`text-xs ${
                        research.impact === "উচ্চ প্রভাব" 
                          ? "bg-green-100 text-green-700" 
                          : "bg-yellow-100 text-yellow-700"
                      }`}
                    >
                      {research.impact}
                    </Badge>
                  </div>
                  <p
                    className="text-gray-600 mb-3"
                    style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                  >
                    {research.description}
                  </p>
                  <p className="text-xs text-gray-500 italic">
                    সূত্র: {research.source}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Country-wise Market Share */}
        <Card className="backdrop-blur-sm bg-white/80 border-blue-500/10">
          <CardContent className="p-8">
            <div className="text-center mb-8">
              <h3
                className="text-2xl font-bold text-gray-900 mb-2"
                style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
              >
                দেশভিত্তিক বাজার অংশীদারিত্ব
              </h3>
              <p
                className="text-gray-600"
                style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
              >
                বৈশ্বিক ভেষজ ওষুধের বাজারে প্রধান দেশগুলোর অবস্থান
              </p>
            </div>
            <div className="space-y-4">
              {countryData.map((country, index) => (
                <div key={index} className="flex items-center gap-4 p-4 bg-blue-50/50 rounded-lg">
                  <div className="w-20 text-center">
                    <div
                      className="text-xl font-bold text-blue-600"
                      style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                    >
                      {country.percentage}
                    </div>
                  </div>
                  <div className="flex-1">
                    <h4
                      className="font-semibold text-gray-900"
                      style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                    >
                      {country.country}
                    </h4>
                    <p
                      className="text-sm text-gray-600"
                      style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                    >
                      {country.description}
                    </p>
                  </div>
                  <div className="w-32">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full transition-all duration-1000"
                        style={{ width: country.percentage }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
}

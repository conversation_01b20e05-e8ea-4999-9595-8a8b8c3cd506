'use client'

import Head from 'next/head'
import { seoConfig } from '@/lib/seo-config'

interface SEOHeadProps {
  title?: string
  description?: string
  keywords?: string[]
  image?: string
  url?: string
  type?: 'website' | 'article' | 'product'
  publishedTime?: string
  modifiedTime?: string
  author?: string
  price?: string
  currency?: string
  availability?: string
  brand?: string
  category?: string
  structuredData?: any
}

export default function SEOHead({
  title,
  description,
  keywords = [],
  image,
  url,
  type = 'website',
  publishedTime,
  modifiedTime,
  author,
  price,
  currency = 'BDT',
  availability = 'InStock',
  brand = 'Oshudhigram',
  category,
  structuredData,
}: SEOHeadProps) {
  const seoTitle = title || seoConfig.defaultTitle
  const seoDescription = description || seoConfig.defaultDescription
  const seoImage = image || `${seoConfig.siteUrl}/og-image.jpg`
  const seoUrl = url || seoConfig.siteUrl
  const allKeywords = [...seoConfig.primaryKeywords, ...keywords].join(', ')

  // Generate product structured data if price is provided
  const productStructuredData = price ? {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": seoTitle,
    "description": seoDescription,
    "image": seoImage,
    "brand": {
      "@type": "Brand",
      "name": brand
    },
    "offers": {
      "@type": "Offer",
      "price": price,
      "priceCurrency": currency,
      "availability": `https://schema.org/${availability}`,
      "url": seoUrl,
      "seller": {
        "@type": "Organization",
        "name": "Oshudhigram - ঔষধি গ্রাম"
      }
    },
    "category": category,
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "reviewCount": "2500"
    }
  } : null

  const finalStructuredData = structuredData || productStructuredData

  return (
    <>
      {/* Primary Meta Tags */}
      <title>{seoTitle}</title>
      <meta name="title" content={seoTitle} />
      <meta name="description" content={seoDescription} />
      <meta name="keywords" content={allKeywords} />
      <meta name="author" content={author || "Oshudhigram Team"} />
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      <meta name="googlebot" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      <meta name="bingbot" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      
      {/* Canonical URL */}
      <link rel="canonical" href={seoUrl} />
      
      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:url" content={seoUrl} />
      <meta property="og:title" content={seoTitle} />
      <meta property="og:description" content={seoDescription} />
      <meta property="og:image" content={seoImage} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:alt" content={seoTitle} />
      <meta property="og:site_name" content="Oshudhigram - ঔষধি গ্রাম" />
      <meta property="og:locale" content="bn_BD" />
      <meta property="og:locale:alternate" content="en_US" />
      
      {/* Article specific meta tags */}
      {type === 'article' && publishedTime && (
        <meta property="article:published_time" content={publishedTime} />
      )}
      {type === 'article' && modifiedTime && (
        <meta property="article:modified_time" content={modifiedTime} />
      )}
      {type === 'article' && author && (
        <meta property="article:author" content={author} />
      )}
      
      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:url" content={seoUrl} />
      <meta property="twitter:title" content={seoTitle} />
      <meta property="twitter:description" content={seoDescription} />
      <meta property="twitter:image" content={seoImage} />
      <meta property="twitter:creator" content="@oshudhigram" />
      
      {/* Additional SEO Meta Tags */}
      <meta name="theme-color" content="#22c55e" />
      <meta name="msapplication-TileColor" content="#22c55e" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="Oshudhigram" />
      
      {/* Geo Tags for Local SEO */}
      <meta name="geo.region" content="BD-50" />
      <meta name="geo.placename" content="Natore, Bangladesh" />
      <meta name="geo.position" content="24.4206;88.9318" />
      <meta name="ICBM" content="24.4206, 88.9318" />
      
      {/* Language and Content */}
      <meta httpEquiv="content-language" content="bn-BD" />
      <meta name="language" content="Bengali" />
      
      {/* Structured Data */}
      {finalStructuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(finalStructuredData),
          }}
        />
      )}
      
      {/* Preconnect for Performance */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://www.google-analytics.com" />
      <link rel="preconnect" href="https://www.googletagmanager.com" />
      
      {/* DNS Prefetch */}
      <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
      <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
      <link rel="dns-prefetch" href="https://www.google-analytics.com" />
      
      {/* Favicon */}
      <link rel="icon" href="/favicon.ico" sizes="any" />
      <link rel="icon" href="/icon.svg" type="image/svg+xml" />
      <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/manifest.json" />
    </>
  )
}

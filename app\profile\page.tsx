"use client"

import { useAuth } from "@/contexts/AuthContext"
import { User, Mail, Calendar, Shield, Activity } from "lucide-react"
import Image from "next/image"

export default function ProfilePage() {
  const { user, userRole, loading } = useAuth()

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Not Signed In</h1>
          <p className="text-gray-600">Please sign in to view your profile.</p>
        </div>
      </div>
    )
  }

  const formatDate = (timestamp: any) => {
    if (!timestamp) return "N/A"

    try {
      const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)
      return date.toLocaleDateString() + " " + date.toLocaleTimeString()
    } catch {
      return "N/A"
    }
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-700 px-6 py-8">
          <div className="flex items-center space-x-4">
            {user.photoURL ? (
              <Image
                src={user.photoURL || "/placeholder.svg"}
                alt={user.displayName || "User"}
                width={80}
                height={80}
                className="rounded-full border-4 border-white"
              />
            ) : (
              <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center">
                <User className="h-10 w-10 text-gray-400" />
              </div>
            )}
            <div className="text-white">
              <h1 className="text-2xl font-bold">{user.displayName}</h1>
              <p className="text-blue-100">{user.email}</p>
              <div className="flex items-center space-x-2 mt-2">
                <div className={`w-3 h-3 rounded-full ${userRole?.isActive ? "bg-green-400" : "bg-gray-400"}`}></div>
                <span className="text-sm text-blue-100">{userRole?.isActive ? "Currently Active" : "Inactive"}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>

              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Email</p>
                  <p className="text-sm text-gray-600">{user.email}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <User className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Display Name</p>
                  <p className="text-sm text-gray-600">{user.displayName || "Not set"}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Calendar className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Member Since</p>
                  <p className="text-sm text-gray-600">{formatDate(userRole?.createdAt)}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Activity className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Last Seen</p>
                  <p className="text-sm text-gray-600">{formatDate(userRole?.lastSeen)}</p>
                </div>
              </div>
            </div>

            {/* Roles and Permissions */}
            <div className="space-y-4">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Roles & Permissions</h2>

              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Shield className="h-5 w-5 text-red-500" />
                    <span className="text-sm font-medium">Administrator</span>
                  </div>
                  <span
                    className={`px-2 py-1 text-xs font-medium rounded-full ${
                      userRole?.isAdmin ? "bg-red-100 text-red-800" : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {userRole?.isAdmin ? "Active" : "Inactive"}
                  </span>
                </div>

                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Shield className="h-5 w-5 text-green-500" />
                    <span className="text-sm font-medium">Seller</span>
                  </div>
                  <span
                    className={`px-2 py-1 text-xs font-medium rounded-full ${
                      userRole?.isSeller ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {userRole?.isSeller ? "Active" : "Inactive"}
                  </span>
                </div>

                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Shield className="h-5 w-5 text-blue-500" />
                    <span className="text-sm font-medium">Moderator</span>
                  </div>
                  <span
                    className={`px-2 py-1 text-xs font-medium rounded-full ${
                      userRole?.isMod ? "bg-blue-100 text-blue-800" : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {userRole?.isMod ? "Active" : "Inactive"}
                  </span>
                </div>
              </div>

              {/* Role Description */}
              <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                <h3 className="text-sm font-medium text-blue-900 mb-2">Your Access Level</h3>
                <p className="text-sm text-blue-800">
                  {userRole?.isAdmin
                    ? "You have full administrative access to the system."
                    : userRole?.isMod
                      ? "You have moderator privileges and can manage users."
                      : userRole?.isSeller
                        ? "You can list and manage products for sale."
                        : "You have standard user access to the platform."}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

import { Metadata } from 'next'
import Image from 'next/image'
import Link from 'next/link'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Calendar, Clock, User, Share2, Heart, Star, CheckCircle, ArrowRight, Leaf } from 'lucide-react'

export const metadata: Metadata = {
  title: 'এলোভেরার ১০টি অবিশ্বাস্য উপকারিতা যা আপনি জানেন না | Oshudhigram',
  description: 'এলোভেরার স্বাস্থ্য উপকারিতা, ত্বকের যত্ন, হজমশক্তি বৃদ্ধি, রোগ প্রতিরোধ ক্ষমতা বাড়ানো এবং প্রাকৃতিক চিকিৎসায় এলোভেরার ব্যবহার সম্পর্কে বিস্তারিত জানুন।',
  keywords: [
    'এলোভেরার উপকারিতা',
    'aloe vera benefits bangla',
    'এলোভেরা জেল ব্যবহার',
    'ত্বকের যত্নে এলোভেরা',
    'এলোভেরার ঔষধি গুণ',
    'প্রাকৃতিক ত্বকের যত্ন',
    'এলোভেরা রস উপকারিতা',
    'হজ<PERSON>শক্তি বৃদ্ধি',
    'রোগ প্রতিরোধ ক্ষমতা',
    'aloe vera gel bangladesh',
    'natural skincare bangladesh',
    'herbal medicine aloe vera'
  ].join(', '),
  openGraph: {
    title: 'এলোভেরার ১০টি অবিশ্বাস্য উপকারিতা | Oshudhigram',
    description: 'এলোভেরার স্বাস্থ্য উপকারিতা ও ব্যবহার সম্পর্কে বিস্তারিত জানুন',
    images: ['/blog/aloe-vera-benefits-detailed.jpg'],
    type: 'article',
    publishedTime: '2025-01-10T00:00:00.000Z',
    authors: ['ডা. রহিমা খাতুন'],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'এলোভেরার ১০টি অবিশ্বাস্য উপকারিতা',
    description: 'এলোভেরার স্বাস্থ্য উপকারিতা ও ব্যবহার',
    images: ['/blog/aloe-vera-benefits-detailed.jpg'],
  },
}

const benefits = [
  {
    title: 'ত্বকের প্রাকৃতিক ময়েশ্চারাইজার',
    description: 'এলোভেরা জেল ত্বকে গভীরভাবে প্রবেশ করে আর্দ্রতা ধরে রাখে এবং শুষ্ক ত্বকের সমস্যা দূর করে।',
    icon: '🌿'
  },
  {
    title: 'ক্ষত নিরাময়ে কার্যকর',
    description: 'এলোভেরার অ্যান্টি-ইনফ্লামেটরি উপাদান কাটা, পোড়া এবং ছোট ক্ষত দ্রুত সারিয়ে তোলে।',
    icon: '🩹'
  },
  {
    title: 'হজমশক্তি বৃদ্ধি',
    description: 'এলোভেরা রস নিয়মিত সেবনে পেটের সমস্যা, কোষ্ঠকাঠিন্য এবং অ্যাসিডিটি কমে।',
    icon: '🫄'
  },
  {
    title: 'রোগ প্রতিরোধ ক্ষমতা বাড়ায়',
    description: 'এলোভেরার অ্যান্টিঅক্সিডেন্ট এবং ভিটামিন শরীরের রোগ প্রতিরোধ ক্ষমতা শক্তিশালী করে।',
    icon: '🛡️'
  },
  {
    title: 'চুলের যত্নে অতুলনীয়',
    description: 'এলোভেরা জেল চুলের গোড়া মজবুত করে, খুশকি দূর করে এবং চুল পড়া রোধ করে।',
    icon: '💇‍♀️'
  },
]

const usageTips = [
  'সকালে খালি পেটে ২-৩ চামচ এলোভেরা রস পান করুন',
  'রাতে ঘুমানোর আগে মুখে এলোভেরা জেল লাগান',
  'চুলে সপ্তাহে ২-৩ বার এলোভেরা জেল ব্যবহার করুন',
  'ছোট কাটা বা পোড়ার জায়গায় সরাসরি এলোভেরা জেল লাগান',
  'ত্বকের শুষ্কতা দূর করতে দিনে ২ বার এলোভেরা জেল ব্যবহার করুন'
]

export default function AloeVeraBenefitsPage() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": "এলোভেরার ১০টি অবিশ্বাস্য উপকারিতা যা আপনি জানেন না",
    "description": "এলোভেরার স্বাস্থ্য উপকারিতা, ত্বকের যত্ন, হজমশক্তি বৃদ্ধি এবং প্রাকৃতিক চিকিৎসায় ব্যবহার",
    "image": "https://oshudhigram.com/blog/aloe-vera-benefits-detailed.jpg",
    "author": {
      "@type": "Person",
      "name": "ডা. রহিমা খাতুন",
      "url": "https://oshudhigram.com/authors/dr-rahima-khatun"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Oshudhigram - ঔষধি গ্রাম",
      "logo": {
        "@type": "ImageObject",
        "url": "https://oshudhigram.com/logo.png"
      }
    },
    "datePublished": "2025-01-10T00:00:00.000Z",
    "dateModified": "2025-01-10T00:00:00.000Z",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": "https://oshudhigram.com/blog/aloe-vera-benefits-bangla"
    },
    "articleSection": "Health",
    "keywords": "এলোভেরার উপকারিতা, aloe vera benefits, ত্বকের যত্ন, প্রাকৃতিক চিকিৎসা",
    "wordCount": 1200,
    "inLanguage": "bn-BD"
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-50 pt-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Breadcrumb */}
          <nav className="mb-8">
            <ol className="flex items-center space-x-2 text-sm text-gray-500">
              <li><Link href="/" className="hover:text-[#10c255]">হোম</Link></li>
              <li>/</li>
              <li><Link href="/blog" className="hover:text-[#10c255]">ব্লগ</Link></li>
              <li>/</li>
              <li className="text-gray-900">এলোভেরার উপকারিতা</li>
            </ol>
          </nav>

          {/* Article Header */}
          <header className="mb-8">
            <div className="flex items-center gap-2 mb-4">
              <Badge className="bg-green-100 text-green-800">এলোভেরা</Badge>
              <Badge variant="outline">স্বাস্থ্য টিপস</Badge>
              <Badge variant="outline">প্রাকৃতিক চিকিৎসা</Badge>
            </div>
            
            <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4" 
                style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
              এলোভেরার ১০টি অবিশ্বাস্য উপকারিতা যা আপনি জানেন না
            </h1>
            
            <div className="flex items-center gap-6 text-sm text-gray-500 mb-6">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span>ডা. রহিমা খাতুন</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>১০ জানুয়ারি, ২০২৫</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <span>৫ মিনিট পড়ার সময়</span>
              </div>
            </div>

            <div className="relative h-64 lg:h-96 rounded-2xl overflow-hidden mb-8">
              <Image
                src="/blog/aloe-vera-benefits-detailed.jpg"
                alt="এলোভেরার উপকারিতা - Oshudhigram"
                fill
                className="object-cover"
                priority
              />
            </div>
          </header>

          {/* Article Content */}
          <article className="prose prose-lg max-w-none">
            <div className="bg-blue-50 border-l-4 border-blue-400 p-6 mb-8 rounded-r-lg">
              <p className="text-blue-800 font-medium" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                <strong>গুরুত্বপূর্ণ তথ্য:</strong> এলোভেরা হাজার বছর ধরে প্রাকৃতিক চিকিৎসায় ব্যবহৃত হয়ে আসছে। 
                এর বৈজ্ঞানিক নাম Aloe barbadensis miller এবং এতে রয়েছে ৭৫+ সক্রিয় উপাদান।
              </p>
            </div>

            <p className="text-lg text-gray-700 mb-6" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
              এলোভেরা শুধুমাত্র একটি সাধারণ গাছ নয়, এটি প্রকৃতির দেওয়া এক অমূল্য উপহার। 
              বিশ্বব্যাপী লাখো মানুষ এলোভেরার উপকারিতা পেয়ে তাদের স্বাস্থ্য ও সৌন্দর্য রক্ষা করছেন। 
              আজকে আমরা জানবো এলোভেরার এমন কিছু উপকারিতা যা হয়তো আপনি আগে জানতেন না।
            </p>

            <h2 className="text-2xl font-bold mb-6 text-gray-900" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
              এলোভেরার প্রধান উপকারিতাসমূহ
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              {benefits.map((benefit, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <span className="text-3xl">{benefit.icon}</span>
                      <div>
                        <h3 className="text-lg font-semibold mb-2" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                          {benefit.title}
                        </h3>
                        <p className="text-gray-600" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                          {benefit.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <h2 className="text-2xl font-bold mb-6 text-gray-900" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
              এলোভেরা ব্যবহারের সঠিক নিয়ম
            </h2>

            <Card className="bg-green-50 border-green-200 mb-8">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                  <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                  দৈনিক ব্যবহারের টিপস
                </h3>
                <ul className="space-y-3">
                  {usageTips.map((tip, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                      <span style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>{tip}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-6 mb-8 rounded-r-lg">
              <h3 className="text-lg font-semibold mb-2 text-yellow-800" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                ⚠️ সতর্কতা
              </h3>
              <ul className="text-yellow-700 space-y-1" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                <li>• গর্ভবতী মহিলারা এলোভেরা রস সেবনের আগে ডাক্তারের পরামর্শ নিন</li>
                <li>• প্রথমবার ব্যবহারের আগে ত্বকে প্যাচ টেস্ট করুন</li>
                <li>• অতিরিক্ত সেবন পেটের সমস্যা সৃষ্টি করতে পারে</li>
              </ul>
            </div>

            <h2 className="text-2xl font-bold mb-6 text-gray-900" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
              বৈজ্ঞানিক গবেষণা ও প্রমাণ
            </h2>

            <p className="text-gray-700 mb-6" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
              আন্তর্জাতিক গবেষণায় প্রমাণিত হয়েছে যে এলোভেরায় রয়েছে অ্যান্টি-ইনফ্লামেটরি, 
              অ্যান্টিব্যাকটেরিয়াল এবং অ্যান্টিঅক্সিডেন্ট গুণাগুণ। জার্নাল অফ ডার্মাটোলজিক্যাল ট্রিটমেন্ট 
              এ প্রকাশিত গবেষণা অনুযায়ী, এলোভেরা জেল ত্বকের আর্দ্রতা ৮৫% পর্যন্ত বৃদ্ধি করতে পারে।
            </p>

            <Card className="bg-gradient-to-r from-[#10c255]/10 to-[#10c255]/5 border-[#10c255]/20 mb-8">
              <CardContent className="p-6 text-center">
                <Leaf className="h-12 w-12 mx-auto mb-4 text-[#10c255]" />
                <h3 className="text-xl font-semibold mb-2" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                  ঔষধি গ্রামের খাঁটি এলোভেরা পণ্য
                </h3>
                <p className="text-gray-600 mb-4" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                  আমাদের কাছে পাবেন ১০০% প্রাকৃতিক ও রাসায়নিক মুক্ত এলোভেরা জেল ও রস
                </p>
                <Button className="bg-[#10c255] hover:bg-[#0ea048]" asChild>
                  <Link href="/shop?category=aloe-vera" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                    এলোভেরা পণ্য দেখুন
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </article>

          {/* Share Buttons */}
          <div className="border-t pt-8 mt-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <span className="text-sm text-gray-500" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                  এই নিবন্ধটি শেয়ার করুন:
                </span>
                <div className="flex gap-2">
                  <Button size="sm" variant="outline" className="text-blue-600 hover:bg-blue-50">
                    Facebook
                  </Button>
                  <Button size="sm" variant="outline" className="text-green-600 hover:bg-green-50">
                    WhatsApp
                  </Button>
                  <Button size="sm" variant="outline" className="text-blue-400 hover:bg-blue-50">
                    Twitter
                  </Button>
                </div>
              </div>
              <Button variant="ghost" size="sm" className="text-red-500 hover:bg-red-50">
                <Heart className="h-4 w-4 mr-1" />
                পছন্দ
              </Button>
            </div>
          </div>

          {/* Related Articles */}
          <section className="mt-12 pt-8 border-t">
            <h2 className="text-2xl font-bold mb-6" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
              আরও পড়ুন
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold mb-2" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                    <Link href="/blog/tulsi-medicinal-properties" className="hover:text-[#10c255]">
                      তুলসী: প্রকৃতির সবচেয়ে শক্তিশালী ওষুধ
                    </Link>
                  </h3>
                  <p className="text-gray-600 text-sm" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                    তুলসীর অ্যান্টিভাইরাল ও অ্যান্টিব্যাকটেরিয়াল গুণাগুণ সম্পর্কে জানুন
                  </p>
                </CardContent>
              </Card>
              
              <Card className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold mb-2" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                    <Link href="/blog/ashwagandha-uses-bangladesh" className="hover:text-[#10c255]">
                      অশ্বগন্ধা: স্ট্রেস ও দুর্বলতার প্রাকৃতিক সমাধান
                    </Link>
                  </h3>
                  <p className="text-gray-600 text-sm" style={{ fontFamily: 'Noto Sans Bengali, sans-serif' }}>
                    অশ্বগন্ধার মানসিক চাপ কমানো ও শক্তি বৃদ্ধির গুণাগুণ
                  </p>
                </CardContent>
              </Card>
            </div>
          </section>
        </div>
      </div>
    </>
  )
}

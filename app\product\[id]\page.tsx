import { getProduct, getProductReviews, getRelatedProducts } from "@/lib/firestore"
import { notFound } from "next/navigation"
import ProductDetailClient from "./ProductDetailClient"

interface ProductPageProps {
  params: {
    id: string
  }
}

export default async function ProductPage({ params }: ProductPageProps) {
  const product = await getProduct(params.id)

  if (!product) {
    notFound()
  }

  const [reviews, relatedProducts] = await Promise.all([
    getProductReviews(params.id),
    getRelatedProducts(params.id, product.category),
  ])

  return <ProductDetailClient product={product} reviews={reviews} relatedProducts={relatedProducts} />
}

export async function generateMetadata({ params }: ProductPageProps) {
  const product = await getProduct(params.id)

  if (!product) {
    return {
      title: "Product Not Found",
    }
  }

  return {
    title: `${product.name} - ShopHub`,
    description: product.description,
  }
}

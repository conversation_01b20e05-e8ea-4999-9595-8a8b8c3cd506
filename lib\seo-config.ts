// SEO Configuration for Oshudhigram - ঔষধি গ্রাম
export const seoConfig = {
    // Base configuration
    siteName: "Oshudhigram - ঔষধি গ্রাম",
    siteUrl: "https://oshudhigram.com",
    defaultTitle: "ঔষধি গ্রাম – নাটোরের হার্বাল ভিলেজ | Oshudhigram - Premium Herbal Products Bangladesh",
    defaultDescription: "ঔষধি গ্রাম - Oshudhigram – নাটোরের বিখ্যাত ঔষধি গ্রাম। এখানে পাবেন ৩০০+ প্রজাতির ভেষজ গাছ যেমন এলোভেরা, তুলসী, অশ্বগন্ধা, কালোমেঘ ও শিমুল মূল। প্রাকৃতিক ও খাঁটি হার্বাল পণ্য অনলাইনে অর্ডার করুন।",
    
    // High-impact keywords for ranking #1
    primaryKeywords: [
      "ঔষধি গ্রাম", "Oshudhigram", "নাটোর ঔষধি গ্রাম", "herbal village bangladesh",
      "এলোভেরা বাংলাদেশ", "তুলসী পণ্য", "অশ্বগন্ধা চাষ", "ভেষজ ওষুধ",
      "প্রাকৃতিক ওষুধ", "হার্বাল চিকিৎসা", "বাংলাদেশ ভেষজ", "natore herbal village"
    ],
    
    // Long-tail keywords for specific searches
    longTailKeywords: [
      "নাটোরে কোথায় পাবো এলোভেরা জেল",
      "বাংলাদেশের সেরা তুলসী পণ্য",
      "অশ্বগন্ধা কোথায় কিনবো বাংলাদেশে",
      "প্রাকৃতিক ভেষজ ওষুধ অনলাইনে কিনুন",
      "নাটোর ঔষধি গ্রাম কিভাবে যাবো",
      "best herbal products in bangladesh",
      "organic aloe vera gel bangladesh price",
      "tulsi leaves benefits in bengali"
    ],
    
    // Location-based SEO
    locationKeywords: [
      "নাটোর", "রাজশাহী", "বাংলাদেশ", "Natore", "Rajshahi", "Bangladesh",
      "নাটোর জেলা", "রাজশাহী বিভাগ", "উত্তরবঙ্গ"
    ],
    
    // Product categories for SEO
    productCategories: [
      {
        name: "এলোভেরা পণ্য",
        slug: "aloe-vera",
        keywords: ["aloe vera gel", "এলোভেরা জেল", "aloe vera juice", "এলোভেরা রস"]
      },
      {
        name: "তুলসী পণ্য", 
        slug: "tulsi",
        keywords: ["tulsi leaves", "তুলসী পাতা", "holy basil", "তুলসী চা"]
      },
      {
        name: "অশ্বগন্ধা",
        slug: "ashwagandha", 
        keywords: ["ashwagandha root", "অশ্বগন্ধা মূল", "winter cherry", "অশ্বগন্ধা পাউডার"]
      }
    ],
    
    // Social media handles
    social: {
      facebook: "https://facebook.com/oshudhigram",
      instagram: "https://instagram.com/oshudhigram", 
      youtube: "https://youtube.com/@oshudhigram",
      twitter: "https://twitter.com/oshudhigram",
      linkedin: "https://linkedin.com/company/oshudhigram"
    },
    
    // Business information
    business: {
      name: "Oshudhigram - ঔষধি গ্রাম",
      address: "Herbal Village, Natore, Rajshahi, Bangladesh",
      phone: "",
      email: "<EMAIL>",
      coordinates: {
        latitude: 24.4206,
        longitude: 88.9318
      },
      hours: "08:00-20:00",
      established: "2020"
    },
    
    // Content themes for blog/SEO content
    contentThemes: [
      "ভেষজ গাছের উপকারিতা",
      "প্রাকৃতিক চিকিৎসা পদ্ধতি", 
      "হার্বাল পণ্যের ব্যবহার",
      "ঔষধি গাছ চাষাবাদ",
      "আয়ুর্বেদিক চিকিৎসা",
      "organic farming techniques",
      "herbal medicine benefits",
      "natural health remedies"
    ]
  };
  
  // Generate structured data for different page types
  export const generateStructuredData = (pageType: string, data?: any) => {
    const baseOrganization = {
      "@type": "Organization",
      "@id": `${seoConfig.siteUrl}/#organization`,
      "name": seoConfig.business.name,
      "url": seoConfig.siteUrl,
      "logo": `${seoConfig.siteUrl}/logo.png`,
      "description": seoConfig.defaultDescription,
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "Herbal Village",
        "addressLocality": "Natore",
        "addressRegion": "Rajshahi", 
        "addressCountry": "BD"
      },
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": seoConfig.business.phone,
        "contactType": "customer service",
        "availableLanguage": ["Bengali", "English"]
      },
      "sameAs": Object.values(seoConfig.social)
    };
  
    switch (pageType) {
      case 'homepage':
        return {
          "@context": "https://schema.org",
          "@graph": [
            baseOrganization,
            {
              "@type": "WebSite",
              "@id": `${seoConfig.siteUrl}/#website`,
              "url": seoConfig.siteUrl,
              "name": seoConfig.siteName,
              "description": seoConfig.defaultDescription,
              "publisher": { "@id": `${seoConfig.siteUrl}/#organization` },
              "potentialAction": {
                "@type": "SearchAction",
                "target": `${seoConfig.siteUrl}/search?q={search_term_string}`,
                "query-input": "required name=search_term_string"
              }
            }
          ]
        };
        
      case 'product':
        return {
          "@context": "https://schema.org",
          "@type": "Product",
          "name": data?.name,
          "description": data?.description,
          "brand": { "@id": `${seoConfig.siteUrl}/#organization` },
          "offers": {
            "@type": "Offer",
            "price": data?.price,
            "priceCurrency": "BDT",
            "availability": "https://schema.org/InStock"
          }
        };
        
      default:
        return baseOrganization;
    }
  };
  
  // SEO utility functions
  export const generateMetaTags = (page: {
    title?: string;
    description?: string;
    keywords?: string[];
    image?: string;
    url?: string;
  }) => {
    return {
      title: page.title || seoConfig.defaultTitle,
      description: page.description || seoConfig.defaultDescription,
      keywords: [...seoConfig.primaryKeywords, ...(page.keywords || [])].join(", "),
      openGraph: {
        title: page.title || seoConfig.defaultTitle,
        description: page.description || seoConfig.defaultDescription,
        url: page.url || seoConfig.siteUrl,
        images: [
          {
            url: page.image || `${seoConfig.siteUrl}/og-image.jpg`,
            width: 1200,
            height: 630,
            alt: page.title || seoConfig.siteName
          }
        ]
      }
    };
  };
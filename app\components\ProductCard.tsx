"use client"

import Image from "next/image"
import Link from "next/link"
import type { Product } from "@/lib/types"
import { ShoppingCart, Plus, Minus, Check, Eye } from "lucide-react"
import { useCart } from "@/contexts/CartContext"
import { useState } from "react"

interface ProductCardProps {
  product: Product
}

export default function ProductCard({ product }: ProductCardProps) {
  const { addToCart, isInCart, getCartItem, updateQuantity } = useCart()
  const [isAdding, setIsAdding] = useState(false)
  const [showSuccess, setShowSuccess] = useState(false)

  const cartItem = getCartItem(product.id)
  const inCart = isInCart(product.id)

  const handleAddToCart = async () => {
    setIsAdding(true)
    addToCart(product, 1)

    // Show success animation
    setShowSuccess(true)
    setTimeout(() => {
      setShowSuccess(false)
      setIsAdding(false)
    }, 1000)
  }

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity <= 0) {
      updateQuantity(product.id, 0)
    } else {
      updateQuantity(product.id, newQuantity)
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 group">
      <div className="relative h-64 w-full overflow-hidden">
        <Image
          src={product.image || "/placeholder.svg?height=256&width=256&query=product"}
          alt={product.name}
          fill
          className="object-cover group-hover:scale-105 transition-transform duration-300"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />

        {/* Quick View Button */}
        <Link
          href={`/product/${product.id}`}
          className="absolute top-4 right-4 bg-white/90 hover:bg-white text-gray-700 p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 shadow-md"
        >
          <Eye className="h-4 w-4" />
        </Link>
      </div>

      <div className="p-4">
        <Link href={`/product/${product.id}`}>
          <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2 hover:text-blue-600 transition-colors">
            {product.name}
          </h3>
        </Link>

        <p className="text-gray-600 text-sm mb-3 line-clamp-2">{product.description}</p>

        <div className="flex items-center justify-between mb-4">
          <span className="text-2xl font-bold text-blue-600">${product.price.toFixed(2)}</span>
          <Link href={`/product/${product.id}`} className="text-sm text-blue-600 hover:text-blue-800 font-medium">
            View Details
          </Link>
        </div>

        {/* Cart Controls */}
        {inCart && cartItem ? (
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleQuantityChange(cartItem.quantity - 1)}
                className="w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center transition-colors"
              >
                <Minus className="h-4 w-4" />
              </button>
              <span className="font-medium text-lg min-w-[2rem] text-center">{cartItem.quantity}</span>
              <button
                onClick={() => handleQuantityChange(cartItem.quantity + 1)}
                className="w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center transition-colors"
              >
                <Plus className="h-4 w-4" />
              </button>
            </div>
            <span className="text-sm text-green-600 font-medium">In Cart</span>
          </div>
        ) : (
          <button
            onClick={handleAddToCart}
            disabled={isAdding}
            className={`w-full flex items-center justify-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200 ${
              showSuccess
                ? "bg-green-600 text-white"
                : isAdding
                  ? "bg-blue-400 text-white cursor-not-allowed"
                  : "bg-blue-600 hover:bg-blue-700 text-white"
            }`}
          >
            {showSuccess ? (
              <>
                <Check className="h-4 w-4" />
                <span className="text-sm font-medium">Added!</span>
              </>
            ) : isAdding ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span className="text-sm font-medium">Adding...</span>
              </>
            ) : (
              <>
                <ShoppingCart className="h-4 w-4" />
                <span className="text-sm font-medium">Add to Cart</span>
              </>
            )}
          </button>
        )}
      </div>
    </div>
  )
}

"use client"

import type React from "react"
import { createContext, useContext, useEffect, useState } from "react"
import type { User } from "firebase/auth"
import { onAuthStateChange, updateUserActivity, type UserRole } from "@/lib/auth"

interface AuthContextType {
  user: User | null
  userRole: UserRole | null
  loading: boolean
  signIn: () => Promise<void>
  signOut: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [userRole, setUserRole] = useState<UserRole | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const unsubscribe = onAuthStateChange((user, userRole) => {
      setUser(user)
      setUserRole(userRole)
      setLoading(false)
    })

    return unsubscribe
  }, [])

  // Update user activity when they're active
  useEffect(() => {
    if (user) {
      // Update activity every 5 minutes
      const interval = setInterval(
        () => {
          updateUserActivity(user.uid, true)
        },
        5 * 60 * 1000,
      )

      // Update activity when page becomes visible
      const handleVisibilityChange = () => {
        if (!document.hidden && user) {
          updateUserActivity(user.uid, true)
        }
      }

      document.addEventListener("visibilitychange", handleVisibilityChange)

      // Update activity when user interacts with the page
      const handleUserActivity = () => {
        if (user) {
          updateUserActivity(user.uid, true)
        }
      }

      window.addEventListener("click", handleUserActivity)
      window.addEventListener("keypress", handleUserActivity)
      window.addEventListener("scroll", handleUserActivity)

      return () => {
        clearInterval(interval)
        document.removeEventListener("visibilitychange", handleVisibilityChange)
        window.removeEventListener("click", handleUserActivity)
        window.removeEventListener("keypress", handleUserActivity)
        window.removeEventListener("scroll", handleUserActivity)
      }
    }
  }, [user])

  // Set user as inactive when they leave
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (user) {
        updateUserActivity(user.uid, false)
      }
    }

    window.addEventListener("beforeunload", handleBeforeUnload)
    return () => window.removeEventListener("beforeunload", handleBeforeUnload)
  }, [user])

  const signIn = async () => {
    const { signInWithGoogle } = await import("@/lib/auth")
    try {
      await signInWithGoogle()
    } catch (error) {
      console.error("Sign in error:", error)
      throw error
    }
  }

  const signOut = async () => {
    const { signOut: firebaseSignOut } = await import("@/lib/auth")
    try {
      await firebaseSignOut()
    } catch (error) {
      console.error("Sign out error:", error)
      throw error
    }
  }

  const value = {
    user,
    userRole,
    loading,
    signIn,
    signOut,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

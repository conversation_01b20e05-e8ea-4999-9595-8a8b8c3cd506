export const dynamic = 'force-static'; 
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Navigation, 
  Car, 
  Bus, 
  Clock, 
  Camera, 
  Home,
} from "lucide-react";

const attractions = [
  {
    name: "মরহুম আফাজ উদ্দিন পাগল এর মাজার ও বাড়ী",
    icon: <Home className="h-5 w-5" />,
    type: "ঐতিহাসিক স্থান"
  }
];

export default function VisitingGuide() {
  return (
    <section className="py-20 bg-gradient-to-b from-green-50/50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <Badge
            className="mb-4 bg-[#10c255]/10 text-[#10c255] border-[#10c255]/20"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            ভ্রমণ গাইড
          </Badge>
          <h2
            className="text-4xl font-bold text-gray-900 mb-4"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            কিভাবে আসবেন ঔষধি গ্রামে
          </h2>
          <p
            className="text-lg text-gray-600"
            style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
          >
            নাটোর থেকে ঔষধি গ্রামে পৌঁছানোর সহজ উপায়
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Transportation Guide */}
          <Card className="backdrop-blur-sm bg-white/80 border-[#10c255]/10">
            <CardContent className="p-8">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-12 h-12 rounded-full bg-[#10c255]/10 flex items-center justify-center">
                  <Navigation className="h-6 w-6 text-[#10c255]" />
                </div>
                <h3
                  className="text-2xl font-semibold"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  যাতায়াত ব্যবস্থা
                </h3>
              </div>

              <div className="space-y-6">
                {/* Route 1 */}
                <div className="border-l-4 border-[#10c255] pl-6">
                  <div className="flex items-center gap-2 mb-2">
                    <Car className="h-5 w-5 text-[#10c255]" />
                    <h4
                      className="text-lg font-semibold"
                      style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                    >
                      রুট ১: অটোরিক্সা
                    </h4>
                  </div>
                  <p
                    className="text-gray-600 leading-relaxed"
                    style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                  >
                    নাটোর থেকে হয়বতপুরে অটোতে যান, তারপর হয়বতপুর থেকে 
                    অটোরিক্সা বা ভ্যান নিয়ে কাঁঠাল বাড়ীয়া নতুন বাজারে চলে আসুন।
                  </p>
                </div>

                {/* Route 2 */}
                <div className="border-l-4 border-blue-500 pl-6">
                  <div className="flex items-center gap-2 mb-2">
                    <Bus className="h-5 w-5 text-blue-500" />
                    <h4
                      className="text-lg font-semibold"
                      style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                    >
                      রুট ২: বাস
                    </h4>
                  </div>
                  <p
                    className="text-gray-600 leading-relaxed"
                    style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                  >
                    বাস থেকে সরাসরি হয়বতপুরে নামুন, তারপর হয়বতপুর থেকে 
                    অটোরিক্সা বা ভ্যান নিয়ে কাঁঠাল বাড়ীয়া নতুন বাজারে যান।
                  </p>
                </div>
              </div>

              <div className="mt-6 p-4 bg-[#10c255]/5 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="h-5 w-5 text-[#10c255]" />
                  <span
                    className="font-semibold text-[#10c255]"
                    style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                  >
                    সময়কাল
                  </span>
                </div>
                <p
                  className="text-sm text-gray-600"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  নাটোর থেকে মোট সময় লাগবে প্রায় ৩০-৪৫ মিনিট
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Attractions */}
          <Card className="backdrop-blur-sm bg-white/80 border-[#10c255]/10">
            <CardContent className="p-8">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-12 h-12 rounded-full bg-[#10c255]/10 flex items-center justify-center">
                  <Camera className="h-6 w-6 text-[#10c255]" />
                </div>
                <h3
                  className="text-2xl font-semibold"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  দর্শনীয় স্থান
                </h3>
              </div>

              <div className="space-y-4 mb-6">
                {attractions.map((attraction, index) => (
                  <div
                    key={index}
                    className="flex items-start gap-3 p-4 bg-[#10c255]/5 rounded-lg hover:bg-[#10c255]/10 transition-colors"
                  >
                    <div className="w-8 h-8 rounded-full bg-[#10c255]/20 flex items-center justify-center flex-shrink-0 mt-1">
                      {attraction.icon}
                    </div>
                    <div>
                      <h4
                        className="font-semibold text-gray-900 mb-1"
                        style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                      >
                        {attraction.name}
                      </h4>
                      <Badge
                        variant="secondary"
                        className="text-xs bg-[#10c255]/10 text-[#10c255]"
                        style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                      >
                        {attraction.type}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>

              <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="h-5 w-5 text-green-600" />
                  <span
                    className="font-semibold text-green-800"
                    style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                  >
                    একদিনে ভ্রমণ
                  </span>
                </div>
                <p
                  className="text-sm text-green-700"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  সব গুলো প্লেস একদিনে দেখা সম্ভব
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}